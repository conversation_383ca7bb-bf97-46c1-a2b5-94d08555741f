#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
毕业论文格式化器 V2 - 完全重新设计
基于jieguo.docx样板，修复所有格式问题
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.section import WD_SECTION
import os
import shutil
from datetime import datetime

class ThesisFormatterV2:
    """毕业论文格式化器 V2 - 修复版本"""
    
    def __init__(self):
        self.document = None
        
    def format_thesis_document(self, source_path, **kwargs):
        """
        格式化毕业论文文档 - V2版本
        """
        
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"源文档不存在: {source_path}")
        
        # 提取参数
        thesis_title = kwargs.get('thesis_title', '毕业论文题目')
        student_name = kwargs.get('student_name', '')
        student_id = kwargs.get('student_id', '')
        department = kwargs.get('department', '智能科学与技术系')
        major = kwargs.get('major', '')
        class_name = kwargs.get('class_name', '')
        supervisor = kwargs.get('supervisor', '')
        supervisor_title = kwargs.get('supervisor_title', '副教授')
        enterprise_supervisor = kwargs.get('enterprise_supervisor', '')
        thesis_date = kwargs.get('thesis_date', '二○二五年七月')
        school_name = kwargs.get('school_name', '江西泰豪动漫职业学院')
        year = kwargs.get('year', '2025')
        output_dir = kwargs.get('output_dir', 'outputs')
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"毕业论文_{student_name or '学生'}_{timestamp}.docx"
        output_path = os.path.join(output_dir, output_filename)
        
        # 创建新文档
        self.document = Document()
        
        try:
            # 1. 设置页面属性
            self._setup_page_settings()
            
            # 2. 创建封面页
            self._create_cover_page(thesis_title, student_name, student_id, department,
                                  major, class_name, supervisor, supervisor_title,
                                  enterprise_supervisor, thesis_date)
            
            # 3. 添加新节，开始诚信声明页
            new_section = self.document.add_section(WD_SECTION.NEW_PAGE)

            # 4. 创建诚信声明页
            self._create_declaration_page(thesis_title, student_name, supervisor)

            # 5. 添加新节，开始摘要页
            new_section = self.document.add_section(WD_SECTION.NEW_PAGE)

            # 6. 创建摘要页
            self._create_abstract_page()

            # 7. 添加新节，开始目录页
            new_section = self.document.add_section(WD_SECTION.NEW_PAGE)

            # 8. 创建目录页
            self._create_table_of_contents()

            # 9. 添加新节，开始正文
            new_section = self.document.add_section(WD_SECTION.NEW_PAGE)

            # 10. 插入原文档内容
            self._insert_original_content(source_path)
            
            # 11. 设置页眉页脚
            self._setup_headers_footers(school_name, year)
            
            # 12. 保存文档
            self.document.save(output_path)
            
            return output_path
            
        except Exception as e:
            if os.path.exists(output_path):
                os.remove(output_path)
            raise e
    
    def _setup_page_settings(self):
        """设置页面属性"""
        section = self.document.sections[0]
        # 设置页边距（基于样板）
        section.top_margin = Inches(0.79)
        section.bottom_margin = Inches(0.79)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
        
        # 设置页面大小（A4）
        section.page_width = Inches(8.27)
        section.page_height = Inches(11.69)
    
    def _create_cover_page(self, thesis_title, student_name, student_id, department,
                          major, class_name, supervisor, supervisor_title,
                          enterprise_supervisor, thesis_date):
        """创建封面页"""
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 毕业论文标题
        title_para = self.document.add_paragraph()
        title_run = title_para.add_run("毕业论文")
        title_run.font.name = '黑体'
        title_run.font.size = Pt(42)
        title_run.font.bold = True
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 论文题目
        thesis_title_para = self.document.add_paragraph()
        thesis_title_run = thesis_title_para.add_run(thesis_title)
        thesis_title_run.font.name = '黑体'
        thesis_title_run.font.size = Pt(22)
        thesis_title_run.font.bold = True
        thesis_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        self.document.add_paragraph()
        
        # 学生信息 - 使用简单的段落格式，确保对齐
        info_items = [
            ("院    系：", department),
            ("姓    名：", student_name),
            ("学    号：", student_id),
            ("专    业：", major),
            ("班    级：", class_name),
            ("指导教师：", supervisor),
            ("职    称：", supervisor_title),
            ("企业导师：", enterprise_supervisor)
        ]
        
        for label, value in info_items:
            info_para = self.document.add_paragraph()
            info_text = f"{label}    {value or ''}"
            info_run = info_para.add_run(info_text)
            info_run.font.name = '宋体'
            info_run.font.size = Pt(14)
            info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        for _ in range(3):
            self.document.add_paragraph()
        
        # 日期
        date_para = self.document.add_paragraph()
        date_run = date_para.add_run(thesis_date)
        date_run.font.name = '楷体_GB2312'
        date_run.font.size = Pt(18)
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    def _create_declaration_page(self, thesis_title, student_name, supervisor):
        """创建诚信声明页"""
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 诚信声明标题
        decl_title_para = self.document.add_paragraph()
        decl_title_run = decl_title_para.add_run("毕业论文诚信声明")
        decl_title_run.font.name = '黑体'
        decl_title_run.font.size = Pt(16)
        decl_title_run.font.bold = True
        decl_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 声明内容
        decl_content_para = self.document.add_paragraph()
        decl_content_run = decl_content_para.add_run("本人郑重声明：")
        decl_content_run.font.name = '宋体'
        decl_content_run.font.size = Pt(14)
        
        decl_text_para = self.document.add_paragraph()
        decl_text = f"所呈交的毕业论文《{thesis_title}》是本人在指导老师的指导下，独立研究、写作的成果。毕业设计(论文)中所引用是他人的无论以何种方式发布的文字、研究成果，均在毕业设计(论文)中以明确方式标明。"
        decl_text_run = decl_text_para.add_run(decl_text)
        decl_text_run.font.name = '宋体'
        decl_text_run.font.size = Pt(14)
        decl_text_para.paragraph_format.first_line_indent = Inches(0.5)
        
        decl_result_para = self.document.add_paragraph()
        decl_result_run = decl_result_para.add_run("本声明的法律结果由本人独自承担。")
        decl_result_run.font.name = '宋体'
        decl_result_run.font.size = Pt(14)
        decl_result_para.paragraph_format.first_line_indent = Inches(0.5)
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 签名栏
        sign_para1 = self.document.add_paragraph()
        sign_run1 = sign_para1.add_run("作  者 签 名：")
        sign_run1.font.name = '宋体'
        sign_run1.font.size = Pt(14)
        
        sign_para2 = self.document.add_paragraph()
        sign_run2 = sign_para2.add_run("指导教师签名：")
        sign_run2.font.name = '宋体'
        sign_run2.font.size = Pt(14)
        
        date_sign_para = self.document.add_paragraph()
        date_sign_run = date_sign_para.add_run("年    月    日")
        date_sign_run.font.name = '宋体'
        date_sign_run.font.size = Pt(14)
    
    def _create_abstract_page(self):
        """创建摘要页"""
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 摘要标题
        abstract_title_para = self.document.add_paragraph()
        abstract_title_run = abstract_title_para.add_run("摘    要")
        abstract_title_run.font.name = '黑体'
        abstract_title_run.font.size = Pt(16)
        abstract_title_run.font.bold = True
        abstract_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        self.document.add_paragraph()
        
        # 摘要内容（示例）
        abstract_paras = [
            "20世纪以来，建构主义成为风靡全球的理论流派，××××××××××××××××××，××××××××××××××××××××××××××××××。×××××××××××，××××××××××××××××××××××××××。",
            "本研究以建构主义作为基本的理论基础，××××××××××××××××××××××××××××××。×××××××××××，××××××××××××××××××××××××××。",
            "…………",
            "××××××，××××××××××××××××××××××××××××××。×××××××××××，××××××××××××××××××××××××××。"
        ]
        
        for abstract_text in abstract_paras:
            abstract_para = self.document.add_paragraph()
            abstract_run = abstract_para.add_run(abstract_text)
            abstract_run.font.name = '宋体'
            abstract_run.font.size = Pt(12)
            abstract_para.paragraph_format.first_line_indent = Inches(0.5)
            abstract_para.paragraph_format.line_spacing = 1.5
        
        # 添加空行
        self.document.add_paragraph()
        
        # 关键词
        keywords_para = self.document.add_paragraph()
        keywords_run = keywords_para.add_run("关键词：课程知识；建构；建构主义；社会建构；个体建构")
        keywords_run.font.name = '宋体'
        keywords_run.font.size = Pt(12)
    
    def _create_table_of_contents(self):
        """创建目录页"""
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 目录标题
        toc_title_para = self.document.add_paragraph()
        toc_title_run = toc_title_para.add_run("目    录")
        toc_title_run.font.name = '黑体'
        toc_title_run.font.size = Pt(16)
        toc_title_run.font.bold = True
        toc_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        self.document.add_paragraph()
        
        # 目录条目（基于jieguo.docx样板，修复对齐问题）
        toc_items = [
            ("中文摘要", "Ⅰ"),
            ("英文摘要", "Ⅱ"),
            ("目    录", "Ⅲ"),
            ("1 引言", "1"),
            ("1.1 研究背景", "4"),
            ("1.2 研究目的与意义", "4"),
            ("1.2.1 研究目的", "10"),
            ("1.2.2 研究意义", "10"),
            ("2 相关技术研究", "18"),
            ("2.1 技术概述", "18"),
            ("2.2 关键技术分析", "25"),
            ("3 系统设计与实现", "35"),
            ("3.1 系统架构设计", "35"),
            ("3.2 功能模块设计", "42"),
            ("4 系统测试与分析", "58"),
            ("4.1 测试环境", "58"),
            ("4.2 测试结果分析", "65"),
            ("5 总结与展望", "78"),
            ("5.1 工作总结", "78"),
            ("5.2 未来展望", "82"),
            ("参考文献", "115"),
            ("致谢", "118")
        ]
        
        for title, page_num in toc_items:
            toc_para = self.document.add_paragraph()
            
            # 设置目录条目样式和缩进
            if title.startswith("1 ") or title.startswith("2 ") or title.startswith("3 ") or title.startswith("4 ") or title.startswith("5 "):
                # 一级标题
                toc_run = toc_para.add_run(title)
                toc_run.font.name = '宋体'
                toc_run.font.size = Pt(12)
                toc_run.font.bold = True
            elif title.startswith("1.") or title.startswith("2.") or title.startswith("3.") or title.startswith("4.") or title.startswith("5."):
                # 二级标题
                toc_run = toc_para.add_run(title)
                toc_run.font.name = '宋体'
                toc_run.font.size = Pt(12)
                toc_para.paragraph_format.left_indent = Inches(0.25)
            else:
                # 其他条目
                toc_run = toc_para.add_run(title)
                toc_run.font.name = '宋体'
                toc_run.font.size = Pt(12)
            
            # 添加页码（使用简单的空格对齐）
            spaces_needed = max(1, 60 - len(title))
            toc_para.add_run(" " * spaces_needed + page_num)
            
            # 设置行间距
            toc_para.paragraph_format.line_spacing = 1.2

    def _insert_original_content(self, source_path):
        """插入原文档内容"""
        try:
            source_doc = Document(source_path)

            # 添加正文标题
            content_title_para = self.document.add_paragraph()
            content_title_run = content_title_para.add_run("正文内容")
            content_title_run.font.name = '黑体'
            content_title_run.font.size = Pt(16)
            content_title_run.font.bold = True
            content_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            self.document.add_paragraph()  # 空行

            # 复制所有段落内容
            for para in source_doc.paragraphs:
                if para.text.strip():  # 只复制非空段落
                    new_para = self.document.add_paragraph()
                    new_para.text = para.text

                    # 设置基本格式
                    for run in new_para.runs:
                        run.font.name = '宋体'
                        run.font.size = Pt(12)

                    # 设置段落格式
                    new_para.paragraph_format.first_line_indent = Inches(0.5)
                    new_para.paragraph_format.line_spacing = 1.5

            # 复制表格
            for table in source_doc.tables:
                new_table = self.document.add_table(len(table.rows), len(table.columns))
                new_table.style = 'Table Grid'

                for i, row in enumerate(table.rows):
                    for j, cell in enumerate(row.cells):
                        new_table.rows[i].cells[j].text = cell.text
                        # 设置表格字体
                        for para in new_table.rows[i].cells[j].paragraphs:
                            for run in para.runs:
                                run.font.name = '宋体'
                                run.font.size = Pt(10)

        except Exception as e:
            print(f"插入原文档内容时出错: {e}")
            # 如果插入失败，添加一个占位符
            placeholder_para = self.document.add_paragraph()
            placeholder_run = placeholder_para.add_run("[原文档内容将在此处显示]")
            placeholder_run.font.name = '宋体'
            placeholder_run.font.size = Pt(12)

    def _setup_headers_footers(self, school_name, year):
        """设置页眉页脚"""
        sections = self.document.sections

        # 为每个节设置页眉页脚
        for i, section in enumerate(sections):
            # 设置页眉（除了第一节封面页）
            if i > 0:  # 跳过封面页
                header = section.header
                header_para = header.paragraphs[0]
                header_para.text = f"{school_name}{year}届学生毕业设计（说明书）"
                header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                if header_para.runs:
                    header_run = header_para.runs[0]
                    header_run.font.name = '宋体'
                    header_run.font.size = Pt(10)

            # 设置页脚
            footer = section.footer
            footer_para = footer.paragraphs[0]

            if i == 0:  # 封面页
                footer_para.text = ""  # 封面页无页脚
            elif i == 1:  # 声明页
                footer_para.text = "I"
            elif i == 2:  # 摘要页
                footer_para.text = "II"
            elif i == 3:  # 目录页
                footer_para.text = "III"
            else:  # 正文页
                footer_para.text = "1"

            footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            if footer_para.runs:
                footer_run = footer_para.runs[0]
                footer_run.font.name = '宋体'
                footer_run.font.size = Pt(10)
