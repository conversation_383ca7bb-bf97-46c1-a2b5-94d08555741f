# 📚 毕业论文格式化系统

一个智能的毕业论文格式化工具，能够自动将Word文档转换为符合学术规范的标准格式。

## 🎯 主要功能

- **智能章节识别**：准确识别章标题，避免将描述性文字误识别
- **格式规范化**：自动应用标准的学术论文格式
- **摘要处理**：正确处理中英文摘要和分类号
- **目录生成**：自动生成规范的目录页面
- **文档保护**：保持原有文档结构和特殊元素

## 📁 项目结构

```
📦 毕业论文格式化系统
├── 📄 thesis_formatter_v3.py     # 核心格式化器（最终版本）
├── 🌐 web_app.py                 # Web应用界面
├── 📄 jieguo.docx                # 测试用源文档
├── 📁 outputs/                   # 输出文件目录
│   └── 📄 毕业论文_学生_*.docx    # 格式化后的文档
├── 📁 uploads/                   # 上传文件目录
├── 📖 使用说明_最终版.md          # 详细使用说明
├── 📋 修复总结.md                # 修复问题总结
└── 📖 README.md                  # 项目说明（本文件）
```

## 🚀 快速开始

### 方法一：Web界面（推荐）

1. **启动Web服务**
   ```bash
   python web_app.py
   ```

2. **访问界面**
   ```
   http://localhost:5000
   ```

3. **使用步骤**
   - 上传Word文档（.docx格式）
   - 填写学生信息
   - 点击"格式化文档"
   - 下载生成的规范文档

### 方法二：Python脚本

```python
from thesis_formatter_v3 import ThesisFormatterV3

# 创建格式化器
formatter = ThesisFormatterV3()

# 设置学生信息
student_info = {
    'name': '学生姓名',
    'student_id': '学号',
    'major': '专业',
    'class_name': '班级',
    'advisor': '指导教师',
    'college': '学院'
}

# 格式化文档
output_path = formatter.format_thesis_document(
    "源文档.docx", 
    student_info=student_info,
    output_name="格式化后的论文"
)
```

## ✨ 核心特性

### 🎯 智能识别
- **章标题识别**：区分真正的章标题和描述性文字
- **摘要范围**：准确识别摘要边界，包含分类号
- **文档结构**：保护文本框、图片等特殊元素

### 📝 格式规范
- **章标题**：居中、黑体、18pt、加粗
- **节标题**：左对齐、黑体、16pt、加粗  
- **正文**：宋体、12pt、首行缩进、1.5倍行距
- **目录**：点线连接、页码右对齐

### 🔧 技术优势
- **精确识别**：避免将"第一章为绪论"等描述文字误识别为标题
- **格式保护**：只调整需要的格式，保持原有布局
- **兼容性强**：支持各种复杂的Word文档结构

## 📋 系统要求

- Python 3.7+
- python-docx
- Flask（Web界面）

## 📖 详细文档

- [使用说明_最终版.md](./使用说明_最终版.md) - 完整使用指南
- [修复总结.md](./修复总结.md) - 技术修复详情

## 🎉 版本信息

- **当前版本**：ThesisFormatterV3 (最终修复版)
- **更新日期**：2025-08-01
- **状态**：✅ 所有已知问题已修复

## 📞 技术支持

如遇问题，请检查：
1. 源文档格式是否为.docx
2. 学生信息是否完整
3. 文档结构是否清晰
4. 系统版本是否为最新

---

*一个专业、可靠的毕业论文格式化解决方案* 🎓
