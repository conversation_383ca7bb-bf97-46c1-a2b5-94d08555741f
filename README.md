# Word文档自动排版系统

这是一个基于Python的Word文档自动排版和目录生成系统，提供Web服务接口。

## 功能特性

- ✅ 自动生成Word文档
- ✅ 支持Markdown格式的标题识别（# ## ### 等）
- ✅ 自动生成目录结构
- ✅ 自定义首页布局
- ✅ 设置页眉页脚
- ✅ 自动排版（行间距、段落间距等）
- ✅ Web服务接口
- ✅ 支持PDF转换（可选）

## 技术栈

- **核心库**: python-docx - Word文档生成
- **Web框架**: Flask - 提供Web服务
- **PDF转换**: docx2pdf - Word转PDF（可选）

## 安装和运行

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 运行Web服务

```bash
python app.py
```

服务将在 `http://localhost:5000` 启动

### 3. 测试功能

```bash
python test_generator.py
```

## 使用方法

### Web界面使用

1. 打开浏览器访问 `http://localhost:5000`
2. 填写文档信息（标题、作者、内容等）
3. 点击"生成Word文档"按钮
4. 文档将自动下载

### API接口使用

#### 生成文档

**POST** `/generate-document`

请求体示例：
```json
{
    "content": "# 第一章 引言\n\n这是第一章的内容。\n\n## 1.1 背景\n\n技术发展背景介绍。",
    "title": "测试文档",
    "author": "张三",
    "company": "某某公司",
    "page_settings": {
        "header_text": "文档页眉",
        "footer_text": "第 {page} 页",
        "first_page_header": "首页页眉",
        "first_page_footer": "首页页脚"
    },
    "style_settings": {
        "title_font_size": 16,
        "heading_font_size": 14,
        "body_font_size": 12,
        "line_spacing": 1.5,
        "paragraph_spacing": 12
    },
    "first_page_settings": {
        "title": "文档标题",
        "subtitle": "副标题",
        "author": "作者",
        "date": "2024年12月"
    }
}
```

#### 健康检查

**GET** `/health`

返回服务状态信息。

## 文档格式说明

### 标题格式

系统支持Markdown格式的标题：

- `# 一级标题` - 对应Word中的Heading 1
- `## 二级标题` - 对应Word中的Heading 2
- `### 三级标题` - 对应Word中的Heading 3
- `#### 四级标题` - 对应Word中的Heading 4
- `##### 五级标题` - 对应Word中的Heading 5
- `###### 六级标题` - 对应Word中的Heading 6

### 段落格式

普通文本将自动格式化为正文段落，支持：
- 自动行间距设置
- 段落间距设置
- 字体大小自定义

## 项目结构

```
docx/
├── app.py                 # Flask主应用
├── word_generator.py      # Word文档生成器核心类
├── test_generator.py      # 测试脚本
├── requirements.txt       # 依赖包列表
├── templates/
│   └── index.html        # Web界面
├── outputs/              # 生成的文档输出目录
└── README.md            # 项目说明
```

## 配置选项

### 页面设置

- `header_text`: 页眉文本
- `footer_text`: 页脚文本（支持 {page} 占位符）
- `first_page_header`: 首页页眉
- `first_page_footer`: 首页页脚

### 样式设置

- `title_font_size`: 标题字体大小
- `heading_font_size`: 标题字体大小
- `body_font_size`: 正文字体大小
- `line_spacing`: 行间距
- `paragraph_spacing`: 段落间距

### 首页设置

- `first_page_title`: 首页标题
- `first_page_subtitle`: 首页副标题
- `first_page_author`: 首页作者
- `first_page_date`: 首页日期

## 扩展功能

### PDF转换

如需PDF转换功能，确保安装了docx2pdf：

```bash
pip install docx2pdf
```

### 自定义样式

可以在 `word_generator.py` 中修改样式设置，支持：
- 字体类型
- 颜色设置
- 对齐方式
- 缩进设置

## 注意事项

1. 确保系统已安装Python 3.7+
2. 生成的大型文档可能需要较长时间
3. 建议在生产环境中使用WSGI服务器（如Gunicorn）
4. 注意文件权限，确保outputs目录可写

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。 