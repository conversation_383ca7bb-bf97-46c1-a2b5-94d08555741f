#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Word文档生成器测试脚本
演示如何使用WordDocumentGenerator类生成格式化的Word文档
"""

from word_generator import WordDocumentGenerator
import os

def test_basic_document_generation():
    """测试基本文档生成功能"""
    
    # 示例文档内容（使用Markdown格式的标题）
    sample_content = """
# 第一章 引言

这是第一章的内容，介绍整个文档的背景和目的。本章将详细阐述研究的重要性和必要性。

## 1.1 研究背景

在这个快速发展的时代，文档处理变得越来越重要。随着信息技术的不断进步，自动化文档生成已成为提高工作效率的重要手段。

### 1.1.1 技术发展

人工智能和机器学习技术的进步为文档处理带来了新的可能性。Python-docx库为我们提供了强大的Word文档操作能力。

### 1.1.2 应用需求

企业和个人用户对于自动化文档生成的需求日益增长，特别是在报告生成、合同制作等场景中。

## 1.2 研究目标

本研究的主要目标是开发一个自动化的文档排版系统，具体包括：

1. 自动生成文档目录
2. 设置页眉页脚
3. 格式化标题和段落
4. 自定义首页布局

# 第二章 技术方案

本章详细介绍技术实现方案和系统架构设计。

## 2.1 系统架构

系统采用模块化设计，包含以下主要组件：

1. 文档解析模块 - 负责解析输入内容
2. 格式处理模块 - 负责应用样式和格式
3. 输出生成模块 - 负责生成最终文档

## 2.2 核心技术

### 2.2.1 Python-docx库

使用python-docx库进行Word文档的生成和格式化。该库提供了丰富的API接口。

### 2.2.2 Flask框架

使用Flask提供Web服务接口，实现RESTful API设计。

# 第三章 实验结果

## 3.1 功能测试

系统成功实现了以下功能：

- ✅ 自动生成目录
- ✅ 设置页眉页脚
- ✅ 格式化标题和段落
- ✅ 自定义首页布局

## 3.2 性能评估

系统在处理大型文档时表现出良好的性能。

# 第四章 结论

## 4.1 总结

本项目成功开发了一个功能完整的Word文档自动排版系统。

## 4.2 未来工作

未来将继续优化系统性能，增加更多功能特性。
"""
    
    # 创建文档生成器实例
    generator = WordDocumentGenerator()
    
    # 生成文档
    doc_path = generator.generate_document(
        content=sample_content,
        title="Word文档自动排版系统测试报告",
        author="张三",
        company="某某科技有限公司",
        header_text="Word文档自动排版系统",
        footer_text="第 {page} 页",
        first_page_header="测试报告",
        first_page_footer="生成时间：2024年",
        title_font_size=18,
        heading_font_size=16,
        body_font_size=12,
        line_spacing=1.5,
        paragraph_spacing=12,
        first_page_title="Word文档自动排版系统",
        first_page_subtitle="测试报告",
        first_page_author="张三",
        first_page_date="2024年12月"
    )
    
    print(f"文档已生成: {doc_path}")
    
    # 获取标题级别信息
    headings = generator.get_heading_levels()
    print("\n文档标题结构:")
    for level, title in headings:
        indent = "  " * (level - 1)
        print(f"{indent}{'#' * level} {title}")

def test_extract_from_existing_docx():
    """测试从现有docx文件提取内容"""
    
    if os.path.exists('testdoc.docx'):
        generator = WordDocumentGenerator()
        
        # 提取现有文档内容
        print("正在提取testdoc.docx的内容...")
        extracted_content = generator.extract_content_from_docx('testdoc.docx')
        
        if extracted_content:
            # 基于提取的内容生成新文档
            doc_path = generator.generate_document(
                content=extracted_content[:2000],  # 只取前2000字符作为示例
                title="基于testdoc.docx的重新排版文档",
                author="系统自动生成",
                company="文档处理系统",
                header_text="重新排版文档",
                footer_text="第 {page} 页",
                first_page_header="自动排版",
                first_page_footer="系统生成",
                first_page_title="重新排版的文档",
                first_page_subtitle="基于testdoc.docx",
                first_page_author="系统自动生成",
                first_page_date="2024年12月"
            )
            
            print(f"基于testdoc.docx生成的新文档: {doc_path}")
        else:
            print("无法提取testdoc.docx的内容")
    else:
        print("testdoc.docx文件不存在")

def test_custom_content():
    """测试自定义内容生成"""
    
    custom_content = """
# 项目计划书

## 项目概述

这是一个重要的项目，需要详细的规划和执行。

## 项目目标

1. 提高工作效率
2. 降低成本
3. 改善用户体验

## 实施计划

### 第一阶段：需求分析

深入分析用户需求，确定功能规格。

### 第二阶段：系统设计

设计系统架构和用户界面。

### 第三阶段：开发实现

按照设计进行系统开发。

### 第四阶段：测试部署

进行系统测试并部署上线。

## 风险评估

项目可能面临以下风险：

- 技术风险
- 时间风险
- 成本风险

## 结论

通过合理的规划和执行，项目将取得成功。
"""
    
    generator = WordDocumentGenerator()
    
    doc_path = generator.generate_document(
        content=custom_content,
        title="项目计划书",
        author="项目经理",
        company="创新科技有限公司",
        header_text="项目计划书",
        footer_text="机密文件 - 第 {page} 页",
        first_page_header="项目计划书",
        first_page_footer="机密文件",
        first_page_title="项目计划书",
        first_page_subtitle="详细规划文档",
        first_page_author="项目经理",
        first_page_date="2024年12月"
    )
    
    print(f"项目计划书已生成: {doc_path}")

if __name__ == "__main__":
    print("开始测试Word文档生成器...")
    
    # 测试基本功能
    print("\n=== 测试基本文档生成 ===")
    test_basic_document_generation()
    
    # 测试从现有文档提取内容
    print("\n=== 测试从现有文档提取内容 ===")
    test_extract_from_existing_docx()
    
    # 测试自定义内容
    print("\n=== 测试自定义内容生成 ===")
    test_custom_content()
    
    print("\n测试完成！")
