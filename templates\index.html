<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>Word文档生成器</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; }
        input, textarea { width: 100%; padding: 8px; }
        textarea { height: 200px; }
        button { padding: 10px 20px; background: #007bff; color: white; border: none; cursor: pointer; }
        .status { margin-top: 20px; padding: 10px; display: none; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
    </style>
</head>
<body>
    <h1>Word文档自动排版系统</h1>
    
    <form id="documentForm">
        <div class="form-group">
            <label>文档标题:</label>
            <input type="text" id="title" value="测试文档">
        </div>
        
        <div class="form-group">
            <label>作者:</label>
            <input type="text" id="author" value="张三">
        </div>
        
        <div class="form-group">
            <label>文档内容:</label>
            <textarea id="content"># 第一章 引言

这是第一章的内容。

## 1.1 背景

技术发展背景介绍。

# 第二章 结论

项目总结。</textarea>
        </div>
        
        <button type="submit">生成Word文档</button>
    </form>
    
    <div id="status" class="status"></div>

    <script>
        document.getElementById('documentForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const data = {
                content: document.getElementById('content').value,
                title: document.getElementById('title').value,
                author: document.getElementById('author').value,
                company: "测试公司"
            };

            const statusDiv = document.getElementById('status');
            statusDiv.style.display = 'block';
            statusDiv.textContent = '正在生成文档...';

            try {
                const response = await fetch('/generate-document', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `${data.title}.docx`;
                    a.click();
                    window.URL.revokeObjectURL(url);

                    statusDiv.className = 'status success';
                    statusDiv.textContent = '文档生成成功！';
                } else {
                    throw new Error('生成失败');
                }
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '错误：' + error.message;
            }
        });
    </script>
</body>
</html> 