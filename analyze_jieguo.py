#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析jieguo.docx样板文件
"""

from docx import Document
import os

def analyze_jieguo_template():
    """详细分析jieguo.docx样板文件"""
    
    file_path = 'jieguo.docx'
    if not os.path.exists(file_path):
        print('❌ jieguo.docx文件不存在')
        return
    
    try:
        doc = Document(file_path)
        file_size = os.path.getsize(file_path)
        
        print('=== jieguo.docx样板文件详细分析 ===')
        print(f'文件大小: {file_size} 字节')
        print(f'段落总数: {len(doc.paragraphs)}')
        print(f'表格总数: {len(doc.tables)}')
        
        print('\n=== 文档结构分析（前50个段落）===')
        for i, para in enumerate(doc.paragraphs[:50]):
            text = para.text.strip()
            if text:
                print(f'{i+1:2d}: [{para.style.name}] {text[:120]}...')
            elif i < 15:  # 只显示前15个空段落
                print(f'{i+1:2d}: [空段落]')
        
        # 分析段落样式分布
        print('\n=== 段落样式统计 ===')
        style_count = {}
        for para in doc.paragraphs:
            style_name = para.style.name
            style_count[style_name] = style_count.get(style_name, 0) + 1
        
        for style, count in sorted(style_count.items(), key=lambda x: x[1], reverse=True):
            print(f'{style}: {count}个段落')
        
        # 检查表格结构
        if doc.tables:
            print('\n=== 表格结构分析 ===')
            for i, table in enumerate(doc.tables):
                print(f'表格{i+1}: {len(table.rows)}行 x {len(table.columns)}列')
                if len(table.rows) > 0 and len(table.columns) > 0:
                    first_cell = table.rows[0].cells[0].text.strip()[:80]
                    print(f'  首个单元格: {first_cell}...')
                    
                    # 显示表格的前几行内容
                    print(f'  表格内容预览:')
                    for row_idx in range(min(3, len(table.rows))):
                        row_content = []
                        for col_idx in range(min(3, len(table.columns))):
                            cell_text = table.rows[row_idx].cells[col_idx].text.strip()[:20]
                            row_content.append(cell_text)
                        print(f'    行{row_idx+1}: {" | ".join(row_content)}')
        
        # 检查页眉页脚
        print('\n=== 页眉页脚分析 ===')
        for i, section in enumerate(doc.sections):
            print(f'节{i+1}:')
            
            # 检查页眉
            if section.header.paragraphs:
                for j, header_para in enumerate(section.header.paragraphs):
                    header_text = header_para.text.strip()
                    if header_text:
                        print(f'  页眉{j+1}: {header_text}')
            
            # 检查页脚
            if section.footer.paragraphs:
                for j, footer_para in enumerate(section.footer.paragraphs):
                    footer_text = footer_para.text.strip()
                    if footer_text:
                        print(f'  页脚{j+1}: {footer_text}')
            
            # 检查首页页眉页脚
            if section.different_first_page_header_footer:
                print(f'  设置了不同的首页页眉页脚')
                if section.first_page_header.paragraphs:
                    for j, fp_header_para in enumerate(section.first_page_header.paragraphs):
                        fp_header_text = fp_header_para.text.strip()
                        if fp_header_text:
                            print(f'  首页页眉{j+1}: {fp_header_text}')
                
                if section.first_page_footer.paragraphs:
                    for j, fp_footer_para in enumerate(section.first_page_footer.paragraphs):
                        fp_footer_text = fp_footer_para.text.strip()
                        if fp_footer_text:
                            print(f'  首页页脚{j+1}: {fp_footer_text}')
            
            # 页面设置
            print(f'  页边距: 上{section.top_margin.inches:.2f}" 下{section.bottom_margin.inches:.2f}" 左{section.left_margin.inches:.2f}" 右{section.right_margin.inches:.2f}"')
            print(f'  页面大小: {section.page_width.inches:.1f}" x {section.page_height.inches:.1f}"')
        
        # 查找特殊内容
        print('\n=== 特殊内容检测 ===')
        
        # 查找目录
        toc_found = False
        for i, para in enumerate(doc.paragraphs):
            if '目录' in para.text or 'Contents' in para.text or 'TABLE OF CONTENTS' in para.text.upper():
                print(f'  发现目录标题在第{i+1}段: {para.text.strip()}')
                toc_found = True
        
        if not toc_found:
            print('  未发现明显的目录标题')
        
        # 查找首页标题
        title_candidates = []
        for i, para in enumerate(doc.paragraphs[:20]):  # 只在前20段中查找
            if para.text.strip() and len(para.text.strip()) > 5:
                style_name = para.style.name.lower()
                if 'title' in style_name or 'heading' in style_name or para.text.strip().isupper():
                    title_candidates.append((i+1, para.text.strip(), para.style.name))
        
        if title_candidates:
            print('  可能的标题段落:')
            for line_num, text, style in title_candidates[:5]:
                print(f'    第{line_num}段 [{style}]: {text[:100]}...')
        
        # 检查字体信息
        print('\n=== 字体样式分析 ===')
        font_info = {}
        for para in doc.paragraphs[:50]:  # 只分析前50段
            for run in para.runs:
                if run.text.strip():
                    font_name = run.font.name or '默认'
                    font_size = run.font.size.pt if run.font.size else '默认'
                    font_key = f'{font_name}-{font_size}pt'
                    font_info[font_key] = font_info.get(font_key, 0) + 1
        
        print('  常用字体样式:')
        for font_style, count in sorted(font_info.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f'    {font_style}: {count}次')
        
        print('\n=== 分析完成 ===')
        print('基于以上分析，我将调整系统以匹配jieguo.docx的格式要求。')
        
    except Exception as e:
        print(f'❌ 分析文档时出错: {e}')
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    analyze_jieguo_template()
