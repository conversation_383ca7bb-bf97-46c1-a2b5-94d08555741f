#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试毕业论文格式化器
基于jieguo.docx样板格式
"""

from thesis_formatter import ThesisFormatter
from docx import Document
import os

def test_thesis_formatting():
    """测试毕业论文格式化功能"""
    print("=== 测试毕业论文格式化器 ===")
    
    source_file = 'testdoc.docx'
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return
    
    formatter = ThesisFormatter()
    
    try:
        # 执行毕业论文格式化
        print("正在按照jieguo.docx样板格式化毕业论文...")
        output_path = formatter.format_thesis_document(
            source_path=source_file,
            thesis_title="基于人工智能的文档处理系统设计与实现",
            student_name="张三",
            student_id="2021001234",
            department="智能科学与技术系",
            major="人工智能技术应用",
            class_name="AI2101班",
            supervisor="李教授",
            supervisor_title="教授",
            enterprise_supervisor="王工程师",
            thesis_date="二○二五年七月",
            school_name="江西泰豪动漫职业学院",
            year="2025"
        )
        
        print(f"✅ 毕业论文格式化成功: {output_path}")
        
        # 分析生成的文档
        print("\n=== 分析生成的毕业论文文档 ===")
        analyze_generated_thesis(output_path)
        
        return output_path
        
    except Exception as e:
        print(f"❌ 毕业论文格式化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def analyze_generated_thesis(doc_path):
    """分析生成的毕业论文文档"""
    if not doc_path or not os.path.exists(doc_path):
        print("❌ 文档不存在，跳过分析")
        return
    
    try:
        doc = Document(doc_path)
        
        print(f"文档路径: {doc_path}")
        print(f"总段落数: {len(doc.paragraphs)}")
        print(f"总表格数: {len(doc.tables)}")
        print(f"总节数: {len(doc.sections)}")
        print(f"文件大小: {os.path.getsize(doc_path)} 字节")
        
        # 分析前30个段落
        print("\n前30个段落内容:")
        for i, para in enumerate(doc.paragraphs[:30]):
            text = para.text.strip()
            if text:
                print(f"{i+1:2d}: [{para.style.name}] {text[:80]}...")
            elif i < 10:
                print(f"{i+1:2d}: [空段落]")
        
        # 分析节结构
        print(f"\n节结构分析:")
        for i, section in enumerate(doc.sections):
            print(f"节{i+1}:")
            
            # 检查页眉
            if section.header.paragraphs:
                for j, header_para in enumerate(section.header.paragraphs):
                    header_text = header_para.text.strip()
                    if header_text:
                        print(f"  页眉{j+1}: {header_text}")
            
            # 检查页脚
            if section.footer.paragraphs:
                for j, footer_para in enumerate(section.footer.paragraphs):
                    footer_text = footer_para.text.strip()
                    if footer_text:
                        print(f"  页脚{j+1}: {footer_text}")
            
            # 检查首页设置
            if section.different_first_page_header_footer:
                print(f"  设置了不同的首页页眉页脚")
            
            print(f"  页边距: 上{section.top_margin.inches:.2f}\" 下{section.bottom_margin.inches:.2f}\" 左{section.left_margin.inches:.2f}\" 右{section.right_margin.inches:.2f}\"")
        
        # 检查表格保留情况
        if doc.tables:
            print(f"\n表格保留情况:")
            for i, table in enumerate(doc.tables):
                print(f"  表格{i+1}: {len(table.rows)}行 x {len(table.columns)}列")
        
        print("\n✅ 毕业论文文档分析完成")
        
    except Exception as e:
        print(f"❌ 分析文档时出错: {str(e)}")

def compare_with_template():
    """与jieguo.docx样板对比"""
    print(f"\n=== 与jieguo.docx样板对比 ===")
    
    template_path = 'jieguo.docx'
    if not os.path.exists(template_path):
        print("❌ 样板文档不存在")
        return
    
    # 找到最新的生成文档
    output_files = [f for f in os.listdir('outputs') if f.startswith('毕业论文_') and f.endswith('.docx')]
    if not output_files:
        print("❌ 未找到生成的毕业论文文档")
        return
    
    latest_file = max(output_files, key=lambda x: os.path.getmtime(os.path.join('outputs', x)))
    generated_path = os.path.join('outputs', latest_file)
    
    try:
        template_doc = Document(template_path)
        generated_doc = Document(generated_path)
        
        print("对比结果:")
        print(f"  样板段落数: {len(template_doc.paragraphs)}")
        print(f"  生成段落数: {len(generated_doc.paragraphs)}")
        print(f"  样板节数: {len(template_doc.sections)}")
        print(f"  生成节数: {len(generated_doc.sections)}")
        print(f"  样板表格数: {len(template_doc.tables)}")
        print(f"  生成表格数: {len(generated_doc.tables)}")
        
        # 对比页眉页脚
        print(f"\n页眉页脚对比:")
        for i, (template_section, generated_section) in enumerate(zip(template_doc.sections, generated_doc.sections)):
            print(f"  节{i+1}:")
            
            # 对比页眉
            template_header = template_section.header.paragraphs[0].text.strip() if template_section.header.paragraphs else ""
            generated_header = generated_section.header.paragraphs[0].text.strip() if generated_section.header.paragraphs else ""
            
            if template_header == generated_header:
                print(f"    ✅ 页眉一致: {template_header}")
            else:
                print(f"    ❌ 页眉不一致:")
                print(f"      样板: {template_header}")
                print(f"      生成: {generated_header}")
            
            # 对比页脚
            template_footer = template_section.footer.paragraphs[0].text.strip() if template_section.footer.paragraphs else ""
            generated_footer = generated_section.footer.paragraphs[0].text.strip() if generated_section.footer.paragraphs else ""
            
            if template_footer == generated_footer:
                print(f"    ✅ 页脚一致: {template_footer}")
            else:
                print(f"    ❌ 页脚不一致:")
                print(f"      样板: {template_footer}")
                print(f"      生成: {generated_footer}")
        
        print(f"\n文件大小对比:")
        print(f"  样板大小: {os.path.getsize(template_path)} 字节")
        print(f"  生成大小: {os.path.getsize(generated_path)} 字节")
        
    except Exception as e:
        print(f"❌ 对比时出错: {str(e)}")

if __name__ == "__main__":
    print("开始测试毕业论文格式化器...")
    
    # 1. 测试毕业论文格式化
    output_doc = test_thesis_formatting()
    
    # 2. 与样板对比
    compare_with_template()
    
    print("\n=== 测试完成 ===")
    print("请检查outputs目录中的毕业论文文档，验证格式是否符合jieguo.docx样板要求。")
