#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
毕业论文格式化器 V3 - 最终修复版本
专注于解决分页和格式问题
"""

from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import os
from datetime import datetime

class ThesisFormatterV3:
    """毕业论文格式化器 V3 - 最终修复版本"""
    
    def __init__(self):
        self.document = None
        
    def format_thesis_document(self, source_path, **kwargs):
        """
        格式化毕业论文文档 - V3版本
        """

        if not os.path.exists(source_path):
            raise FileNotFoundError(f"源文档不存在: {source_path}")

        # 提取基本参数
        thesis_title = kwargs.get('thesis_title', '毕业论文题目')
        student_name = kwargs.get('student_name', '')
        student_id = kwargs.get('student_id', '')
        department = kwargs.get('department', '智能科学与技术系')
        major = kwargs.get('major', '')
        class_name = kwargs.get('class_name', '')
        supervisor = kwargs.get('supervisor', '')
        supervisor_title = kwargs.get('supervisor_title', '副教授')
        enterprise_supervisor = kwargs.get('enterprise_supervisor', '')
        thesis_date = kwargs.get('thesis_date', '二○二五年七月')
        school_name = kwargs.get('school_name', '江西泰豪动漫职业学院')
        year = kwargs.get('year', '2025')
        output_dir = kwargs.get('output_dir', 'outputs')

        # 提取字体大小参数
        self.font_sizes = {
            'cover_main_title': kwargs.get('cover_main_title_size', 42),  # 封面主标题"毕业论文"
            'cover_thesis_title': kwargs.get('cover_thesis_title_size', 22),  # 封面论文题目
            'cover_info': kwargs.get('cover_info_size', 14),  # 封面学生信息
            'cover_date': kwargs.get('cover_date_size', 18),  # 封面日期
            'section_title': kwargs.get('section_title_size', 16),  # 各部分标题（摘要、目录等）
            'chinese_abstract': kwargs.get('chinese_abstract_size', 12),  # 中文摘要正文
            'english_abstract': kwargs.get('english_abstract_size', 12),  # 英文摘要正文
            'toc_content': kwargs.get('toc_content_size', 12),  # 目录内容
            'body_text': kwargs.get('body_text_size', 12),  # 正文内容
            'table_text': kwargs.get('table_text_size', 10),  # 表格文字
            'header_footer': kwargs.get('header_footer_size', 10),  # 页眉页脚
        }
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"毕业论文_{student_name or '学生'}_{timestamp}.docx"
        output_path = os.path.join(output_dir, output_filename)
        
        # 创建新文档
        self.document = Document()
        
        try:
            # 1. 设置页面属性
            self._setup_page_settings()
            
            # 2. 创建封面页
            self._create_cover_page(thesis_title, student_name, student_id, department,
                                  major, class_name, supervisor, supervisor_title,
                                  enterprise_supervisor, thesis_date)
            
            # 3. 分页符 + 诚信声明页
            self.document.add_page_break()
            self._create_declaration_page(thesis_title, student_name, supervisor)
            
            # 4. 分页符 + 中文摘要页（从源文件提取）
            self.document.add_page_break()
            self._extract_and_create_chinese_abstract_page(source_path)

            # 5. 分页符 + 英文摘要页（从源文件提取）
            self.document.add_page_break()
            self._extract_and_create_english_abstract_page(source_path)
            
            # 6. 分页符 + 目录页
            self.document.add_page_break()
            self._create_table_of_contents(source_path)

            # 7. 分页符 + 正文内容
            self.document.add_page_break()
            self._insert_original_content(source_path)
            
            # 7. 设置页眉页脚
            self._setup_headers_footers(school_name, year)
            
            # 8. 保存文档
            self.document.save(output_path)
            
            return output_path
            
        except Exception as e:
            if os.path.exists(output_path):
                os.remove(output_path)
            raise e
    
    def _setup_page_settings(self):
        """设置页面属性"""
        section = self.document.sections[0]
        # 设置页边距（基于样板）
        section.top_margin = Inches(0.79)
        section.bottom_margin = Inches(0.79)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
        
        # 设置页面大小（A4）
        section.page_width = Inches(8.27)
        section.page_height = Inches(11.69)
    
    def _create_cover_page(self, thesis_title, student_name, student_id, department,
                          major, class_name, supervisor, supervisor_title,
                          enterprise_supervisor, thesis_date):
        """创建封面页"""
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 毕业论文标题
        title_para = self.document.add_paragraph()
        title_run = title_para.add_run("毕业论文")
        title_run.font.name = '黑体'
        title_run.font.size = Pt(self.font_sizes['cover_main_title'])
        title_run.font.bold = True
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()

        # 论文题目
        thesis_title_para = self.document.add_paragraph()
        thesis_title_run = thesis_title_para.add_run(thesis_title)
        thesis_title_run.font.name = '黑体'
        thesis_title_run.font.size = Pt(self.font_sizes['cover_thesis_title'])
        thesis_title_run.font.bold = True
        thesis_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        self.document.add_paragraph()
        
        # 学生信息 - 使用表格确保完美对齐
        table = self.document.add_table(rows=8, cols=2)
        table.style = 'Table Grid'
        
        # 设置表格无边框
        for row in table.rows:
            for cell in row.cells:
                # 设置单元格边框为无
                tc = cell._tc
                tcPr = tc.get_or_add_tcPr()
                tcBorders = tcPr.find('.//{http://schemas.openxmlformats.org/wordprocessingml/2006/main}tcBorders')
                if tcBorders is None:
                    from docx.oxml import parse_xml
                    tcBorders = parse_xml('<w:tcBorders xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">'
                                        '<w:top w:val="nil"/><w:left w:val="nil"/><w:bottom w:val="nil"/><w:right w:val="nil"/>'
                                        '</w:tcBorders>')
                    tcPr.append(tcBorders)
        
        info_items = [
            ("院    系：", department),
            ("姓    名：", student_name),
            ("学    号：", student_id),
            ("专    业：", major),
            ("班    级：", class_name),
            ("指导教师：", supervisor),
            ("职    称：", supervisor_title),
            ("企业导师：", enterprise_supervisor)
        ]
        
        for i, (label, value) in enumerate(info_items):
            # 标签列
            label_cell = table.rows[i].cells[0]
            label_para = label_cell.paragraphs[0]
            label_run = label_para.add_run(label)
            label_run.font.name = '宋体'
            label_run.font.size = Pt(self.font_sizes['cover_info'])
            label_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT

            # 内容列
            content_cell = table.rows[i].cells[1]
            content_para = content_cell.paragraphs[0]
            if value:
                content_run = content_para.add_run(f"   {value}")
                content_run.font.name = '宋体'
                content_run.font.size = Pt(self.font_sizes['cover_info'])
            content_para.alignment = WD_ALIGN_PARAGRAPH.LEFT
        
        # 设置表格居中
        table.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        for _ in range(3):
            self.document.add_paragraph()
        
        # 日期
        date_para = self.document.add_paragraph()
        date_run = date_para.add_run(thesis_date)
        date_run.font.name = '楷体_GB2312'
        date_run.font.size = Pt(self.font_sizes['cover_date'])
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    def _create_declaration_page(self, thesis_title, student_name, supervisor):
        """创建诚信声明页"""
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 诚信声明标题
        decl_title_para = self.document.add_paragraph()
        decl_title_run = decl_title_para.add_run("毕业论文诚信声明")
        decl_title_run.font.name = '黑体'
        decl_title_run.font.size = Pt(self.font_sizes['section_title'])
        decl_title_run.font.bold = True
        decl_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 声明内容
        decl_content_para = self.document.add_paragraph()
        decl_content_run = decl_content_para.add_run("本人郑重声明：")
        decl_content_run.font.name = '宋体'
        decl_content_run.font.size = Pt(self.font_sizes['body_text'])

        decl_text_para = self.document.add_paragraph()
        decl_text = f"所呈交的毕业论文《{thesis_title}》是本人在指导老师的指导下，独立研究、写作的成果。毕业设计(论文)中所引用是他人的无论以何种方式发布的文字、研究成果，均在毕业设计(论文)中以明确方式标明。"
        decl_text_run = decl_text_para.add_run(decl_text)
        decl_text_run.font.name = '宋体'
        decl_text_run.font.size = Pt(self.font_sizes['body_text'])
        decl_text_para.paragraph_format.first_line_indent = Inches(0.5)

        decl_result_para = self.document.add_paragraph()
        decl_result_run = decl_result_para.add_run("本声明的法律结果由本人独自承担。")
        decl_result_run.font.name = '宋体'
        decl_result_run.font.size = Pt(self.font_sizes['body_text'])
        decl_result_para.paragraph_format.first_line_indent = Inches(0.5)
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 签名栏（右对齐，带下划线）
        sign_para1 = self.document.add_paragraph()
        sign_run1 = sign_para1.add_run("作  者 签 名：___________________")
        sign_run1.font.name = '宋体'
        sign_run1.font.size = Pt(self.font_sizes['body_text'])
        sign_para1.alignment = WD_ALIGN_PARAGRAPH.RIGHT

        sign_para2 = self.document.add_paragraph()
        sign_run2 = sign_para2.add_run("指导教师签名：___________________")
        sign_run2.font.name = '宋体'
        sign_run2.font.size = Pt(self.font_sizes['body_text'])
        sign_para2.alignment = WD_ALIGN_PARAGRAPH.RIGHT

        # 添加空行
        self.document.add_paragraph()

        date_sign_para = self.document.add_paragraph()
        date_sign_run = date_sign_para.add_run("年    月    日")
        date_sign_run.font.name = '宋体'
        date_sign_run.font.size = Pt(self.font_sizes['body_text'])
        date_sign_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT
    
    def _extract_and_create_chinese_abstract_page(self, source_path):
        """从源文件提取中文摘要并创建摘要页"""

        try:
            source_doc = Document(source_path)

            # 查找摘要范围
            abstract_start = -1
            abstract_end = -1

            for i, para in enumerate(source_doc.paragraphs):
                text = para.text.strip()
                if text:
                    if ('摘要' in text or '摘 要' in text) and abstract_start == -1:
                        abstract_start = i
                    elif abstract_start != -1 and '关键词' in text:
                        abstract_end = i
                        break

            if abstract_start != -1 and abstract_end != -1:
                # 添加空行
                for _ in range(2):
                    self.document.add_paragraph()

                # 复制摘要内容
                for i in range(abstract_start, abstract_end + 1):
                    source_para = source_doc.paragraphs[i]
                    text = source_para.text.strip()

                    if text:
                        new_para = self.document.add_paragraph()
                        new_run = new_para.add_run(text)

                        # 设置格式
                        if '摘要' in text or '摘 要' in text:
                            # 摘要标题
                            new_run.font.name = '黑体'
                            new_run.font.size = Pt(self.font_sizes['section_title'])
                            new_run.font.bold = True
                            new_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        elif '关键词' in text:
                            # 关键词
                            new_run.font.name = '宋体'
                            new_run.font.size = Pt(self.font_sizes['chinese_abstract'])
                        else:
                            # 摘要正文
                            new_run.font.name = '宋体'
                            new_run.font.size = Pt(self.font_sizes['chinese_abstract'])
                            new_para.paragraph_format.first_line_indent = Inches(0.5)
                            new_para.paragraph_format.line_spacing = 1.5

                print(f"✅ 成功提取摘要内容（第{abstract_start+1}段到第{abstract_end+1}段）")
            else:
                # 如果没找到摘要，创建默认摘要
                self._create_default_abstract()
                print("⚠️ 未找到源文件中的摘要，使用默认摘要")

        except Exception as e:
            print(f"❌ 提取摘要时出错: {e}")
            # 出错时创建默认摘要
            self._create_default_abstract()

    def _create_default_abstract(self):
        """创建默认摘要（备用方案）"""

        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()

        # 摘要标题
        abstract_title_para = self.document.add_paragraph()
        abstract_title_run = abstract_title_para.add_run("摘    要")
        abstract_title_run.font.name = '黑体'
        abstract_title_run.font.size = Pt(self.font_sizes['section_title'])
        abstract_title_run.font.bold = True
        abstract_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        self.document.add_paragraph()

        # 默认摘要内容
        default_abstract = "本研究基于相关理论和方法，对研究问题进行了深入分析和探讨。通过理论研究和实证分析，得出了相关结论，为相关领域的发展提供了参考。"

        abstract_para = self.document.add_paragraph()
        abstract_run = abstract_para.add_run(default_abstract)
        abstract_run.font.name = '宋体'
        abstract_run.font.size = Pt(self.font_sizes['chinese_abstract'])
        abstract_para.paragraph_format.first_line_indent = Inches(0.5)
        abstract_para.paragraph_format.line_spacing = 1.5

        # 添加空行
        self.document.add_paragraph()

        # 默认关键词
        keywords_para = self.document.add_paragraph()
        keywords_run = keywords_para.add_run("关键词：研究；分析；方法")
        keywords_run.font.name = '宋体'
        keywords_run.font.size = Pt(self.font_sizes['chinese_abstract'])

    def _extract_and_create_english_abstract_page(self, source_path):
        """从源文件提取英文摘要并创建英文摘要页"""

        try:
            source_doc = Document(source_path)

            # 查找英文摘要范围
            abstract_start = -1
            abstract_end = -1

            for i, para in enumerate(source_doc.paragraphs):
                text = para.text.strip()
                if text:
                    if 'Abstract' in text and abstract_start == -1:
                        abstract_start = i
                    elif abstract_start != -1 and 'Keywords' in text:
                        abstract_end = i
                        break

            if abstract_start != -1 and abstract_end != -1:
                # 添加空行
                for _ in range(2):
                    self.document.add_paragraph()

                # 复制英文摘要内容
                for i in range(abstract_start, abstract_end + 1):
                    source_para = source_doc.paragraphs[i]
                    text = source_para.text.strip()

                    if text:
                        new_para = self.document.add_paragraph()
                        new_run = new_para.add_run(text)

                        # 设置格式
                        if 'Abstract' in text:
                            # 英文摘要标题
                            new_run.font.name = 'Times New Roman'
                            new_run.font.size = Pt(self.font_sizes['section_title'])
                            new_run.font.bold = True
                            new_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
                        elif 'Keywords' in text:
                            # 英文关键词
                            new_run.font.name = 'Times New Roman'
                            new_run.font.size = Pt(self.font_sizes['english_abstract'])
                        else:
                            # 英文摘要正文
                            new_run.font.name = 'Times New Roman'
                            new_run.font.size = Pt(self.font_sizes['english_abstract'])
                            new_para.paragraph_format.first_line_indent = Inches(0.5)
                            new_para.paragraph_format.line_spacing = 1.5

                print(f"✅ 成功提取英文摘要内容（第{abstract_start+1}段到第{abstract_end+1}段）")
            else:
                # 如果没找到英文摘要，创建默认英文摘要
                self._create_default_english_abstract()
                print("⚠️ 未找到源文件中的英文摘要，使用默认英文摘要")

        except Exception as e:
            print(f"❌ 提取英文摘要时出错: {e}")
            # 出错时创建默认英文摘要
            self._create_default_english_abstract()

    def _create_default_english_abstract(self):
        """创建默认英文摘要（备用方案）"""

        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()

        # 英文摘要标题
        abstract_title_para = self.document.add_paragraph()
        abstract_title_run = abstract_title_para.add_run("Abstract")
        abstract_title_run.font.name = 'Times New Roman'
        abstract_title_run.font.size = Pt(self.font_sizes['section_title'])
        abstract_title_run.font.bold = True
        abstract_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        self.document.add_paragraph()

        # 默认英文摘要内容
        default_abstract = "This research is based on relevant theories and methods, conducting in-depth analysis and discussion of research issues. Through theoretical research and empirical analysis, relevant conclusions are drawn, providing references for the development of related fields."

        abstract_para = self.document.add_paragraph()
        abstract_run = abstract_para.add_run(default_abstract)
        abstract_run.font.name = 'Times New Roman'
        abstract_run.font.size = Pt(self.font_sizes['english_abstract'])
        abstract_para.paragraph_format.first_line_indent = Inches(0.5)
        abstract_para.paragraph_format.line_spacing = 1.5

        # 添加空行
        self.document.add_paragraph()

        # 默认英文关键词
        keywords_para = self.document.add_paragraph()
        keywords_run = keywords_para.add_run("Keywords: Research; Analysis; Method")
        keywords_run.font.name = 'Times New Roman'
        keywords_run.font.size = Pt(self.font_sizes['english_abstract'])

    def _create_table_of_contents(self, source_path=None):
        """创建目录页（基于实际文档结构）"""

        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()

        # 目录标题
        toc_title_para = self.document.add_paragraph()
        toc_title_run = toc_title_para.add_run("目    录")
        toc_title_run.font.name = '黑体'
        toc_title_run.font.size = Pt(self.font_sizes['section_title'])
        toc_title_run.font.bold = True
        toc_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        self.document.add_paragraph()

        # 获取实际的目录条目
        if source_path:
            toc_items = self._extract_toc_from_document(source_path)
        else:
            # 默认目录条目（备用）
            toc_items = [
                ("中文摘要", "Ⅰ", 0),
                ("英文摘要", "Ⅱ", 0),
                ("目    录", "Ⅲ", 0),
                ("1 引言", "1", 1),
                ("1.1 研究背景", "4", 2),
                ("1.2 研究目的与意义", "4", 2),
                ("1.2.1 研究目的", "10", 3),
                ("1.2.2 研究意义", "10", 3),
                ("2 相关技术研究", "18", 1),
                ("2.1 技术概述", "18", 2),
                ("2.2 关键技术分析", "25", 2),
                ("3 系统设计与实现", "35", 1),
                ("3.1 系统架构设计", "35", 2),
                ("3.2 功能模块设计", "42", 2),
                ("4 系统测试与分析", "58", 1),
                ("4.1 测试环境", "58", 2),
                ("4.2 测试结果分析", "65", 2),
                ("5 总结与展望", "78", 1),
                ("5.1 工作总结", "78", 2),
                ("5.2 未来展望", "82", 2),
                ("参考文献", "115", 0),
                ("致谢", "118", 0)
            ]

        # 使用表格来确保目录对齐
        toc_table = self.document.add_table(rows=len(toc_items), cols=2)
        toc_table.style = 'Table Grid'

        # 设置表格无边框
        for row in toc_table.rows:
            for cell in row.cells:
                tc = cell._tc
                tcPr = tc.get_or_add_tcPr()
                from docx.oxml import parse_xml
                tcBorders = parse_xml('<w:tcBorders xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">'
                                    '<w:top w:val="nil"/><w:left w:val="nil"/><w:bottom w:val="nil"/><w:right w:val="nil"/>'
                                    '</w:tcBorders>')
                tcPr.append(tcBorders)

        for i, item in enumerate(toc_items):
            if len(item) == 3:
                title, page_num, level = item
            else:
                title, page_num = item
                level = 1  # 默认级别

            # 标题列
            title_cell = toc_table.rows[i].cells[0]
            title_para = title_cell.paragraphs[0]
            title_run = title_para.add_run(title)
            title_run.font.name = '宋体'
            title_run.font.size = Pt(self.font_sizes['toc_content'])

            # 根据级别设置缩进和字体样式
            if level == 1:  # 章标题
                title_run.font.bold = True
                title_para.paragraph_format.left_indent = Inches(0)
            elif level == 2:  # 节标题
                title_para.paragraph_format.left_indent = Inches(0.25)
            elif level == 3:  # 子节标题
                title_para.paragraph_format.left_indent = Inches(0.5)
            else:  # 其他（摘要、目录等）
                title_para.paragraph_format.left_indent = Inches(0)

            # 页码列
            page_cell = toc_table.rows[i].cells[1]
            page_para = page_cell.paragraphs[0]
            page_run = page_para.add_run(str(page_num))
            page_run.font.name = '宋体'
            page_run.font.size = Pt(self.font_sizes['toc_content'])
            page_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT

    def _extract_toc_from_document(self, source_path):
        """从源文档中提取目录结构"""
        import re

        try:
            source_doc = Document(source_path)

            # 跳过摘要部分
            chinese_abstract_start = -1
            chinese_abstract_end = -1
            english_abstract_start = -1
            english_abstract_end = -1

            for i, para in enumerate(source_doc.paragraphs):
                text = para.text.strip()
                if text:
                    if ('摘要' in text or '摘 要' in text) and chinese_abstract_start == -1:
                        chinese_abstract_start = i
                    elif chinese_abstract_start != -1 and '关键词' in text and chinese_abstract_end == -1:
                        chinese_abstract_end = i
                    elif 'Abstract' in text and english_abstract_start == -1:
                        english_abstract_start = i
                    elif english_abstract_start != -1 and 'Keywords' in text:
                        english_abstract_end = i
                        break

            # 提取章节结构
            toc_items = [
                ("中文摘要", "Ⅰ", 0),
                ("英文摘要", "Ⅱ", 0),
                ("目    录", "Ⅲ", 0),
            ]

            # 正则表达式模式
            chapter_pattern = r'^第[一二三四五六七八九十\d]+章\s*(.+)$|^第\d+章\s*(.+)$'
            section_pattern = r'^(\d+\.\d+)\s*(.+)$'
            subsection_pattern = r'^(\d+\.\d+\.\d+)\s*(.+)$'
            subsubsection_pattern = r'^(\d+\.\d+\.\d+\.\d+)\s*(.+)$'

            page_counter = 1

            for i, para in enumerate(source_doc.paragraphs):
                text = para.text.strip()

                # 跳过摘要部分
                if ((chinese_abstract_start != -1 and chinese_abstract_end != -1 and
                     chinese_abstract_start <= i <= chinese_abstract_end) or
                    (english_abstract_start != -1 and english_abstract_end != -1 and
                     english_abstract_start <= i <= english_abstract_end)):
                    continue

                if text:
                    # 检查四级标题 (*******)
                    subsubsection_match = re.match(subsubsection_pattern, text)
                    if subsubsection_match:
                        toc_items.append((text, page_counter, 4))
                        page_counter += 1
                        continue

                    # 检查三级标题 (1.1.1)
                    subsection_match = re.match(subsection_pattern, text)
                    if subsection_match:
                        toc_items.append((text, page_counter, 3))
                        page_counter += 1
                        continue

                    # 检查二级标题 (1.1)
                    section_match = re.match(section_pattern, text)
                    if section_match:
                        toc_items.append((text, page_counter, 2))
                        page_counter += 2
                        continue

                    # 检查章标题
                    chapter_match = re.match(chapter_pattern, text)
                    if chapter_match:
                        # 清理章标题
                        clean_title = text
                        if '第' in text and '章' in text:
                            # 提取章号和标题
                            parts = text.split('章', 1)
                            if len(parts) == 2:
                                chapter_num = parts[0].replace('第', '').strip()
                                chapter_title = parts[1].strip()
                                if chapter_title:
                                    clean_title = f"{chapter_num} {chapter_title}"
                                else:
                                    clean_title = f"{chapter_num}章"

                        toc_items.append((clean_title, page_counter, 1))
                        page_counter += 3
                        continue

            # 添加结尾部分
            toc_items.extend([
                ("参考文献", page_counter, 0),
                ("致谢", page_counter + 2, 0)
            ])

            print(f"✅ 从文档中提取了 {len(toc_items)} 个目录条目")
            return toc_items

        except Exception as e:
            print(f"❌ 提取目录时出错: {e}")
            # 返回默认目录
            return [
                ("中文摘要", "Ⅰ", 0),
                ("英文摘要", "Ⅱ", 0),
                ("目    录", "Ⅲ", 0),
                ("1 引言", "1", 1),
                ("参考文献", "115", 0),
                ("致谢", "118", 0)
            ]

    def _insert_original_content(self, source_path):
        """插入原文档内容（跳过中英文摘要部分，因为摘要已经单独处理）"""
        try:
            source_doc = Document(source_path)

            # 查找中文摘要范围，以便跳过
            chinese_abstract_start = -1
            chinese_abstract_end = -1

            for i, para in enumerate(source_doc.paragraphs):
                text = para.text.strip()
                if text:
                    if ('摘要' in text or '摘 要' in text) and chinese_abstract_start == -1:
                        chinese_abstract_start = i
                    elif chinese_abstract_start != -1 and '关键词' in text:
                        chinese_abstract_end = i
                        break

            # 查找英文摘要范围，以便跳过
            english_abstract_start = -1
            english_abstract_end = -1

            for i, para in enumerate(source_doc.paragraphs):
                text = para.text.strip()
                if text:
                    if 'Abstract' in text and english_abstract_start == -1:
                        english_abstract_start = i
                    elif english_abstract_start != -1 and 'Keywords' in text:
                        english_abstract_end = i
                        break

            # 添加正文标题
            content_title_para = self.document.add_paragraph()
            content_title_run = content_title_para.add_run("正文内容")
            content_title_run.font.name = '黑体'
            content_title_run.font.size = Pt(self.font_sizes['section_title'])
            content_title_run.font.bold = True
            content_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            self.document.add_paragraph()  # 空行

            # 复制所有段落内容（跳过中英文摘要部分）
            for i, para in enumerate(source_doc.paragraphs):
                if para.text.strip():  # 只复制非空段落
                    # 如果这个段落属于中文摘要部分，跳过
                    if (chinese_abstract_start != -1 and chinese_abstract_end != -1 and
                        chinese_abstract_start <= i <= chinese_abstract_end):
                        continue

                    # 如果这个段落属于英文摘要部分，跳过
                    if (english_abstract_start != -1 and english_abstract_end != -1 and
                        english_abstract_start <= i <= english_abstract_end):
                        continue

                    new_para = self.document.add_paragraph()
                    new_para.text = para.text

                    # 设置基本格式
                    for run in new_para.runs:
                        run.font.name = '宋体'
                        run.font.size = Pt(self.font_sizes['body_text'])

                    # 设置段落格式
                    new_para.paragraph_format.first_line_indent = Inches(0.5)
                    new_para.paragraph_format.line_spacing = 1.5

            # 复制表格
            for table in source_doc.tables:
                new_table = self.document.add_table(len(table.rows), len(table.columns))
                new_table.style = 'Table Grid'

                for i, row in enumerate(table.rows):
                    for j, cell in enumerate(row.cells):
                        new_table.rows[i].cells[j].text = cell.text
                        # 设置表格字体
                        for para in new_table.rows[i].cells[j].paragraphs:
                            for run in para.runs:
                                run.font.name = '宋体'
                                run.font.size = Pt(self.font_sizes['table_text'])

        except Exception as e:
            print(f"插入原文档内容时出错: {e}")
            # 如果插入失败，添加一个占位符
            placeholder_para = self.document.add_paragraph()
            placeholder_run = placeholder_para.add_run("[原文档内容将在此处显示]")
            placeholder_run.font.name = '宋体'
            placeholder_run.font.size = Pt(12)

    def _setup_headers_footers(self, school_name, year):
        """设置页眉页脚（简化处理）"""
        section = self.document.sections[0]

        # 设置页眉
        header = section.header
        header_para = header.paragraphs[0]
        header_para.text = f"{school_name}{year}届学生毕业设计（说明书）"
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if header_para.runs:
            header_run = header_para.runs[0]
            header_run.font.name = '宋体'
            header_run.font.size = Pt(self.font_sizes['header_footer'])

        # 设置页脚
        footer = section.footer
        footer_para = footer.paragraphs[0]
        footer_para.text = "1"
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if footer_para.runs:
            footer_run = footer_para.runs[0]
            footer_run.font.name = '宋体'
            footer_run.font.size = Pt(self.font_sizes['header_footer'])
