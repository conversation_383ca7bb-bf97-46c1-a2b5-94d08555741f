#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析文档结构，提取章节信息
"""

from docx import Document
import re

def analyze_document_structure():
    """分析testdoc.docx的章节结构"""
    
    print('=== 分析文档章节结构 ===')
    
    source_doc = Document('testdoc.docx')
    
    # 跳过摘要部分，只分析正文
    chinese_abstract_start = -1
    chinese_abstract_end = -1
    english_abstract_start = -1
    english_abstract_end = -1
    
    # 找到摘要范围
    for i, para in enumerate(source_doc.paragraphs):
        text = para.text.strip()
        if text:
            if ('摘要' in text or '摘 要' in text) and chinese_abstract_start == -1:
                chinese_abstract_start = i
            elif chinese_abstract_start != -1 and '关键词' in text and chinese_abstract_end == -1:
                chinese_abstract_end = i
            elif 'Abstract' in text and english_abstract_start == -1:
                english_abstract_start = i
            elif english_abstract_start != -1 and 'Keywords' in text:
                english_abstract_end = i
                break
    
    print(f'中文摘要范围: 第{chinese_abstract_start+1}段 到 第{chinese_abstract_end+1}段')
    print(f'英文摘要范围: 第{english_abstract_start+1}段 到 第{english_abstract_end+1}段')
    
    # 分析正文章节结构
    chapters = []
    sections = []
    subsections = []
    
    # 正则表达式模式
    chapter_pattern = r'^第[一二三四五六七八九十\d]+章\s*(.+)$'  # 第X章 标题
    section_pattern = r'^(\d+\.\d+)\s*(.+)$'  # 1.1 标题
    subsection_pattern = r'^(\d+\.\d+\.\d+)\s*(.+)$'  # 1.1.1 标题
    
    print('\n=== 正文章节结构分析 ===')
    
    for i, para in enumerate(source_doc.paragraphs):
        text = para.text.strip()
        
        # 跳过摘要部分
        if ((chinese_abstract_start != -1 and chinese_abstract_end != -1 and 
             chinese_abstract_start <= i <= chinese_abstract_end) or
            (english_abstract_start != -1 and english_abstract_end != -1 and 
             english_abstract_start <= i <= english_abstract_end)):
            continue
        
        if text:
            # 检查是否是章标题
            chapter_match = re.match(chapter_pattern, text)
            if chapter_match:
                chapter_title = chapter_match.group(1)
                chapters.append({
                    'level': 1,
                    'number': len(chapters) + 1,
                    'title': text,
                    'clean_title': chapter_title,
                    'paragraph': i + 1
                })
                print(f'第{len(chapters)}章: {text} (第{i+1}段)')
                continue
            
            # 检查是否是三级标题 (1.1.1)
            subsection_match = re.match(subsection_pattern, text)
            if subsection_match:
                number = subsection_match.group(1)
                title = subsection_match.group(2)
                subsections.append({
                    'level': 3,
                    'number': number,
                    'title': text,
                    'clean_title': title,
                    'paragraph': i + 1
                })
                print(f'  子标题: {text} (第{i+1}段)')
                continue
            
            # 检查是否是二级标题 (1.1)
            section_match = re.match(section_pattern, text)
            if section_match:
                number = section_match.group(1)
                title = section_match.group(2)
                sections.append({
                    'level': 2,
                    'number': number,
                    'title': text,
                    'clean_title': title,
                    'paragraph': i + 1
                })
                print(f'  分标题: {text} (第{i+1}段)')
                continue
    
    print(f'\n=== 统计结果 ===')
    print(f'章节数量: {len(chapters)}')
    print(f'分标题数量: {len(sections)}')
    print(f'子标题数量: {len(subsections)}')
    
    # 合并所有标题并按段落顺序排序
    all_headings = chapters + sections + subsections
    all_headings.sort(key=lambda x: x['paragraph'])
    
    print(f'\n=== 完整章节结构（按出现顺序）===')
    for heading in all_headings:
        indent = '  ' * (heading['level'] - 1)
        print(f'{indent}{heading["title"]} (第{heading["paragraph"]}段)')
    
    return all_headings

if __name__ == "__main__":
    analyze_document_structure()
