#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全元素处理测试脚本
测试文本、表格、图片、公式等所有复杂元素的处理功能
"""

from word_generator import WordDocumentGenerator
import os

def test_comprehensive_document():
    """测试包含所有元素类型的综合文档"""
    print("=== 测试综合文档处理功能 ===")
    
    # 创建包含各种元素的测试内容
    comprehensive_content = """
# 综合测试文档

## 1. 文本内容测试

这是一个包含各种复杂元素的测试文档，用于验证系统的完整功能。

### 1.1 普通段落

这是普通的段落文本，包含中文和English混合内容。系统应该能够正确处理各种字符编码。

### 1.2 特殊格式文本

这里包含一些特殊符号：α、β、γ、∑、∫、√、≤、≥、≠等数学符号。

## 2. 表格测试

### 2.1 简单表格

<!-- 表格开始 -->
| 项目 | 数值 | 单位 | 备注 |
| --- | --- | --- | --- |
| 长度 | 100 | 米 | 测量值 |
| 宽度 | 50 | 米 | 估算值 |
| 高度 | 20 | 米 | 设计值 |
<!-- 表格结束 -->

### 2.2 复杂数据表格

<!-- 表格开始 -->
| 指标名称 | Q1 | Q2 | Q3 | Q4 | 年度总计 | 增长率 |
| --- | --- | --- | --- | --- | --- | --- |
| 营业收入 | 1000万 | 1200万 | 1500万 | 1800万 | 5500万 | 15.2% |
| 净利润 | 100万 | 150万 | 200万 | 250万 | 700万 | 25.8% |
| 研发投入 | 50万 | 60万 | 75万 | 90万 | 275万 | 20.0% |
<!-- 表格结束 -->

## 3. 媒体元素测试

### 3.1 图片元素

[图片: test_image_001]

上图展示了系统架构的整体设计。

[图片: chart_revenue_2024]

这是2024年收入趋势图表。

### 3.2 公式元素

[公式: E = mc²]

这是著名的质能方程。

[公式: ∑(i=1 to n) xi = x1 + x2 + ... + xn]

这是求和公式的一般形式。

### 3.3 绘图元素

[绘图元素: 流程图_业务流程]

[绘图元素: 组织架构图]

## 4. 混合内容测试

### 4.1 文本与表格混合

以下是项目进度统计表：

<!-- 表格开始 -->
| 阶段 | 开始时间 | 结束时间 | 完成度 | 负责人 |
| --- | --- | --- | --- | --- |
| 需求分析 | 2024-01-01 | 2024-01-15 | 100% | 张三 |
| 系统设计 | 2024-01-16 | 2024-02-15 | 100% | 李四 |
| 开发实现 | 2024-02-16 | 2024-04-15 | 80% | 王五 |
| 测试验收 | 2024-04-16 | 2024-05-15 | 30% | 赵六 |
<!-- 表格结束 -->

根据上表可以看出，项目整体进度良好。

### 4.2 图片与公式混合

系统的核心算法如下：

[公式: f(x) = ax² + bx + c]

对应的图形表示为：

[图片: 二次函数图像]

## 5. 总结

本文档成功测试了以下功能：

1. ✅ 多级标题处理
2. ✅ 普通段落格式化
3. ✅ 表格结构保持
4. ✅ 图片占位符处理
5. ✅ 公式占位符处理
6. ✅ 绘图元素占位符处理
7. ✅ 混合内容排版

系统能够完整地处理复杂文档的各种元素，并生成格式化的Word文档。
"""
    
    generator = WordDocumentGenerator()
    
    try:
        # 生成综合测试文档
        doc_path = generator.generate_document(
            content=comprehensive_content,
            title="全元素综合测试文档",
            author="测试系统",
            company="文档处理测试中心",
            header_text="综合测试文档",
            footer_text="第 {page} 页 | 全元素测试",
            first_page_header="全元素处理测试",
            first_page_footer="包含表格、图片、公式等",
            first_page_title="全元素综合测试文档",
            first_page_subtitle="验证系统对复杂元素的处理能力",
            first_page_author="文档处理测试系统",
            title_font_size=20,
            heading_font_size=16,
            body_font_size=12,
            line_spacing=1.5,
            paragraph_spacing=12
        )
        
        print(f"综合测试文档生成成功: {doc_path}")
        return doc_path
        
    except Exception as e:
        print(f"综合测试文档生成失败: {str(e)}")
        return None

def test_real_document_processing():
    """测试真实文档的完整处理流程"""
    print("\n=== 测试真实文档完整处理流程 ===")
    
    generator = WordDocumentGenerator()
    
    # 1. 提取testdoc.docx的内容
    print("1. 提取testdoc.docx的内容...")
    extracted_content = generator.extract_content_from_docx('testdoc.docx')
    
    if not extracted_content:
        print("内容提取失败")
        return
    
    print(f"   提取成功，内容长度: {len(extracted_content)} 字符")
    
    # 2. 分析提取的内容
    print("2. 分析提取的内容...")
    table_count = extracted_content.count('<!-- 表格开始 -->')
    image_count = extracted_content.count('[图片:')
    equation_count = extracted_content.count('[公式:')
    drawing_count = extracted_content.count('[绘图元素:')
    
    print(f"   发现表格: {table_count} 个")
    print(f"   发现图片: {image_count} 个")
    print(f"   发现公式: {equation_count} 个")
    print(f"   发现绘图元素: {drawing_count} 个")
    
    # 3. 生成完整的重排版文档
    print("3. 生成完整的重排版文档...")
    try:
        doc_path = generator.generate_document(
            content=extracted_content,
            title="testdoc完整重排版文档（包含所有元素）",
            author="智能文档处理系统",
            company="自动化排版解决方案",
            header_text="完整重排版 | 保留所有元素",
            footer_text="第 {page} 页 | 智能处理",
            first_page_header="智能文档重排版系统",
            first_page_footer="保留原始内容的所有元素",
            first_page_title="testdoc完整重排版文档",
            first_page_subtitle="基于原始文档的智能重排版\n保留表格、图片、公式等所有元素",
            first_page_author="智能文档处理系统",
            title_font_size=18,
            heading_font_size=16,
            body_font_size=12,
            line_spacing=1.5,
            paragraph_spacing=12
        )
        
        print(f"   重排版文档生成成功: {doc_path}")
        
        # 4. 分析生成的文档
        print("4. 分析生成的文档...")
        from docx import Document
        doc = Document(doc_path)
        
        print(f"   生成文档段落数: {len(doc.paragraphs)}")
        print(f"   生成文档表格数: {len(doc.tables)}")
        print(f"   文档文件大小: {os.path.getsize(doc_path)} 字节")
        
        return doc_path
        
    except Exception as e:
        print(f"   重排版文档生成失败: {str(e)}")
        return None

def compare_documents():
    """比较原始文档和重排版文档"""
    print("\n=== 比较原始文档和重排版文档 ===")
    
    try:
        from docx import Document
        
        # 分析原始文档
        original_doc = Document('testdoc.docx')
        print("原始文档 (testdoc.docx):")
        print(f"  段落数: {len(original_doc.paragraphs)}")
        print(f"  表格数: {len(original_doc.tables)}")
        print(f"  文件大小: {os.path.getsize('testdoc.docx')} 字节")
        
        # 查找最新的重排版文档
        output_files = [f for f in os.listdir('outputs') if f.startswith('testdoc') and f.endswith('.docx')]
        if output_files:
            latest_file = max(output_files, key=lambda x: os.path.getmtime(os.path.join('outputs', x)))
            latest_path = os.path.join('outputs', latest_file)
            
            regenerated_doc = Document(latest_path)
            print(f"\n重排版文档 ({latest_file}):")
            print(f"  段落数: {len(regenerated_doc.paragraphs)}")
            print(f"  表格数: {len(regenerated_doc.tables)}")
            print(f"  文件大小: {os.path.getsize(latest_path)} 字节")
            
            # 比较表格数量
            if len(original_doc.tables) == len(regenerated_doc.tables):
                print("  ✅ 表格数量保持一致")
            else:
                print("  ❌ 表格数量不一致")
            
            # 比较表格结构
            for i, (orig_table, regen_table) in enumerate(zip(original_doc.tables, regenerated_doc.tables)):
                if len(orig_table.rows) == len(regen_table.rows) and len(orig_table.columns) == len(regen_table.columns):
                    print(f"  ✅ 表格{i+1}结构保持一致 ({len(orig_table.rows)}行 x {len(orig_table.columns)}列)")
                else:
                    print(f"  ❌ 表格{i+1}结构不一致")
        else:
            print("未找到重排版文档")
            
    except Exception as e:
        print(f"比较文档时出错: {str(e)}")

if __name__ == "__main__":
    print("开始全元素处理测试...")
    
    # 1. 测试综合文档处理
    comprehensive_doc = test_comprehensive_document()
    
    # 2. 测试真实文档处理
    real_doc = test_real_document_processing()
    
    # 3. 比较文档
    compare_documents()
    
    print("\n=== 测试总结 ===")
    if comprehensive_doc:
        print(f"✅ 综合测试文档生成成功: {os.path.basename(comprehensive_doc)}")
    else:
        print("❌ 综合测试文档生成失败")
    
    if real_doc:
        print(f"✅ 真实文档重排版成功: {os.path.basename(real_doc)}")
    else:
        print("❌ 真实文档重排版失败")
    
    print("\n请检查outputs目录中的生成文档，验证所有元素是否正确处理。")
