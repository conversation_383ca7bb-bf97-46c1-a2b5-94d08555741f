#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终API测试 - 中英文摘要版本
"""

import requests
import os
from docx import Document

def final_api_test():
    """最终API测试"""
    
    print('=== 最终API测试（中英文摘要版本）===')
    
    url = 'http://localhost:5000/format-thesis'
    
    # 准备文件和数据
    files = {'file': open('testdoc.docx', 'rb')}
    data = {
        'thesis_title': '基于人工智能的文档处理系统设计与实现',
        'student_name': '最终API测试',
        'student_id': '2021001247',
        'department': '智能科学与技术系',
        'major': '人工智能技术应用',
        'class_name': 'AI2101班',
        'supervisor': 'API教授',
        'supervisor_title': '教授',
        'enterprise_supervisor': 'API工程师',
        'school_name': '江西泰豪动漫职业学院',
        'year': '2025',
        'thesis_date': '二○二五年七月'
    }
    
    try:
        print('正在测试最终版本API...')
        response = requests.post(url, files=files, data=data)
        
        if response.status_code == 200:
            # 保存返回的文件
            with open('final_api_thesis.docx', 'wb') as f:
                f.write(response.content)
            print('✅ 最终版本API测试成功！')
            print(f'文件已保存: final_api_thesis.docx')
            print(f'文件大小: {len(response.content)} 字节')
            
            # 分析生成的文档
            doc = Document('final_api_thesis.docx')
            print(f'段落数: {len(doc.paragraphs)}')
            print(f'表格数: {len(doc.tables)}')
            
            # 检查文档结构
            print('\n=== 文档结构检查 ===')
            sections = []
            
            for i, para in enumerate(doc.paragraphs[:60]):
                text = para.text.strip()
                if text:
                    if '毕业论文' in text and '声明' not in text:
                        sections.append(f'封面页: 第{i+1}段')
                    elif '诚信声明' in text:
                        sections.append(f'诚信声明: 第{i+1}段')
                    elif '摘 要' in text or '摘要' in text:
                        sections.append(f'中文摘要: 第{i+1}段')
                    elif 'Abstract' in text:
                        sections.append(f'英文摘要: 第{i+1}段')
                    elif '目    录' in text:
                        sections.append(f'目录: 第{i+1}段')
                    elif '正文内容' in text:
                        sections.append(f'正文: 第{i+1}段')
            
            for section in sections:
                print(f'  {section}')
            
            # 检查中英文摘要内容
            print('\n=== 中英文摘要内容检查 ===')
            
            chinese_count = 0
            english_count = 0
            content_chinese_count = 0
            
            for para in doc.paragraphs:
                text = para.text
                if '北京证券交易所' in text:
                    if any(keyword in text for keyword in ['摘要', '关键词']):
                        chinese_count += 1
                    else:
                        content_chinese_count += 1
                
                if 'Beijing Stock Exchange' in text or 'BSE' in text:
                    if any(keyword in text for keyword in ['Abstract', 'Keywords']):
                        english_count += 1
            
            print(f'中文摘要区域"北京证券交易所"出现次数: {chinese_count}')
            print(f'英文摘要区域"Beijing Stock Exchange/BSE"出现次数: {english_count}')
            print(f'正文区域"北京证券交易所"出现次数: {content_chinese_count}')
            
            # 验证结果
            if len(sections) >= 6:
                print('\n✅ 文档结构完整：封面、声明、中文摘要、英文摘要、目录、正文')
            else:
                print('\n❌ 文档结构不完整')
            
            if chinese_count >= 1 and english_count >= 1:
                print('✅ 中英文摘要内容正确提取')
            else:
                print('❌ 中英文摘要内容提取有问题')
            
            if content_chinese_count > 0:
                print('✅ 正文内容完整保留')
            else:
                print('❌ 正文内容可能有问题')
            
        else:
            print(f'❌ API测试失败: {response.status_code}')
            print(f'错误信息: {response.text}')
            
    except Exception as e:
        print(f'❌ 测试过程中出错: {str(e)}')
    finally:
        files['file'].close()

if __name__ == "__main__":
    final_api_test()
