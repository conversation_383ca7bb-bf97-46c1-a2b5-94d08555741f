#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
文档重排版器 - 基于原文档直接修改
保留所有原始元素（图片、表格、公式等），只修改格式和布局
"""

from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.section import WD_ORIENT
from docx.oxml.shared import OxmlElement, qn
import os
import shutil
from datetime import datetime

class DocumentReformatter:
    """文档重排版器 - 直接修改原文档"""
    
    def __init__(self):
        self.document = None
        self.original_path = None
        
    def reformat_document(self, source_path, 
                         title="重排版文档", 
                         author="作者", 
                         company="公司名称",
                         header_text="文档页眉", 
                         footer_text="第 {page} 页",
                         first_page_header="首页页眉", 
                         first_page_footer="首页页脚",
                         first_page_title=None,
                         first_page_subtitle="副标题",
                         first_page_author=None,
                         first_page_date=None,
                         add_toc=True,
                         output_dir="outputs"):
        """
        重排版文档 - 基于原文档直接修改
        
        Args:
            source_path: 源文档路径
            title: 文档标题
            author: 作者
            company: 公司名称
            header_text: 页眉文本
            footer_text: 页脚文本
            first_page_header: 首页页眉
            first_page_footer: 首页页脚
            first_page_title: 首页标题（默认使用title）
            first_page_subtitle: 首页副标题
            first_page_author: 首页作者（默认使用author）
            first_page_date: 首页日期
            add_toc: 是否添加目录
            output_dir: 输出目录
            
        Returns:
            str: 生成的文档路径
        """
        
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"源文档不存在: {source_path}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"{title}_重排版_{timestamp}.docx"
        output_path = os.path.join(output_dir, output_filename)
        
        # 复制原文档到输出位置
        shutil.copy2(source_path, output_path)
        
        # 打开复制的文档进行修改
        self.document = Document(output_path)
        self.original_path = source_path
        
        try:
            # 1. 设置页面属性
            self._setup_page_settings()
            
            # 2. 添加首页（在原内容前插入）
            if first_page_title or first_page_subtitle:
                self._insert_cover_page(
                    first_page_title or title,
                    first_page_subtitle,
                    first_page_author or author,
                    first_page_date or datetime.now().strftime('%Y年%m月%d日'),
                    company
                )
            
            # 3. 设置页眉页脚
            self._setup_headers_footers(header_text, footer_text, first_page_header, first_page_footer)
            
            # 4. 添加目录（在首页后，正文前）
            if add_toc:
                self._insert_table_of_contents()
            
            # 5. 优化原文档格式
            self._optimize_document_formatting()
            
            # 6. 保存修改后的文档
            self.document.save(output_path)
            
            return output_path
            
        except Exception as e:
            # 如果处理失败，删除输出文件
            if os.path.exists(output_path):
                os.remove(output_path)
            raise e
    
    def _setup_page_settings(self):
        """设置页面属性"""
        for section in self.document.sections:
            # 设置页边距
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            section.left_margin = Inches(1.25)
            section.right_margin = Inches(1.25)
            
            # 设置页面方向和大小
            section.orientation = WD_ORIENT.PORTRAIT
            section.page_width = Inches(8.5)
            section.page_height = Inches(11)
    
    def _insert_cover_page(self, title, subtitle, author, date, company):
        """在文档开头插入首页"""
        # 在文档开头插入段落
        first_para = self.document.paragraphs[0]
        
        # 插入首页标题
        title_para = first_para.insert_paragraph_before()
        title_run = title_para.add_run(title)
        title_run.font.size = Pt(24)
        title_run.font.bold = True
        title_run.font.name = '宋体'
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 插入空行
        for _ in range(2):
            first_para.insert_paragraph_before()
        
        # 插入副标题
        if subtitle:
            subtitle_para = first_para.insert_paragraph_before()
            subtitle_run = subtitle_para.add_run(subtitle)
            subtitle_run.font.size = Pt(18)
            subtitle_run.font.name = '宋体'
            subtitle_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 插入空行
            for _ in range(2):
                first_para.insert_paragraph_before()
        
        # 插入更多空行使内容居中
        for _ in range(8):
            first_para.insert_paragraph_before()
        
        # 插入作者信息
        author_para = first_para.insert_paragraph_before()
        author_run = author_para.add_run(f"作者：{author}")
        author_run.font.size = Pt(14)
        author_run.font.name = '宋体'
        author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 插入空行
        first_para.insert_paragraph_before()
        
        # 插入公司信息
        company_para = first_para.insert_paragraph_before()
        company_run = company_para.add_run(company)
        company_run.font.size = Pt(14)
        company_run.font.name = '宋体'
        company_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 插入空行
        for _ in range(2):
            first_para.insert_paragraph_before()
        
        # 插入日期
        date_para = first_para.insert_paragraph_before()
        date_run = date_para.add_run(date)
        date_run.font.size = Pt(14)
        date_run.font.name = '宋体'
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 插入分页符
        page_break_para = first_para.insert_paragraph_before()
        page_break_run = page_break_para.add_run()
        page_break_run.add_break(break_type=6)  # 分页符
    
    def _setup_headers_footers(self, header_text, footer_text, first_page_header, first_page_footer):
        """设置页眉页脚"""
        section = self.document.sections[0]
        
        # 设置不同的首页页眉页脚
        section.different_first_page_header_footer = True
        
        # 首页页眉
        first_header = section.first_page_header
        first_header_para = first_header.paragraphs[0]
        first_header_para.text = first_page_header
        first_header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if first_header_para.runs:
            first_header_run = first_header_para.runs[0]
            first_header_run.font.size = Pt(10)
            first_header_run.font.name = '宋体'
        
        # 首页页脚
        first_footer = section.first_page_footer
        first_footer_para = first_footer.paragraphs[0]
        first_footer_para.text = first_page_footer
        first_footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if first_footer_para.runs:
            first_footer_run = first_footer_para.runs[0]
            first_footer_run.font.size = Pt(10)
            first_footer_run.font.name = '宋体'
        
        # 其他页面页眉
        header = section.header
        header_para = header.paragraphs[0]
        header_para.text = header_text
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if header_para.runs:
            header_run = header_para.runs[0]
            header_run.font.size = Pt(10)
            header_run.font.name = '宋体'
        
        # 其他页面页脚（简化处理）
        footer = section.footer
        footer_para = footer.paragraphs[0]
        footer_para.text = footer_text.replace('{page}', '1')  # 简化处理
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if footer_para.runs:
            footer_run = footer_para.runs[0]
            footer_run.font.size = Pt(10)
            footer_run.font.name = '宋体'
    
    def _insert_table_of_contents(self):
        """插入目录"""
        # 找到首页后的位置插入目录
        insert_position = 0
        
        # 寻找合适的插入位置（首页后）
        for i, para in enumerate(self.document.paragraphs):
            # 如果找到分页符后的第一个段落，在此处插入目录
            if i > 20:  # 跳过首页内容
                insert_position = i
                break
        
        if insert_position > 0:
            target_para = self.document.paragraphs[insert_position]
            
            # 插入目录标题
            toc_title_para = target_para.insert_paragraph_before()
            toc_title_run = toc_title_para.add_run("目录")
            toc_title_run.font.size = Pt(16)
            toc_title_run.font.bold = True
            toc_title_run.font.name = '宋体'
            toc_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 插入空行
            target_para.insert_paragraph_before()
            
            # 生成目录条目
            self._generate_toc_entries(target_para)
            
            # 插入分页符
            page_break_para = target_para.insert_paragraph_before()
            page_break_run = page_break_para.add_run()
            page_break_run.add_break(break_type=6)  # 分页符
    
    def _generate_toc_entries(self, target_para):
        """生成目录条目"""
        # 扫描文档中的标题
        headings = []
        for para in self.document.paragraphs:
            style_name = para.style.name.lower()
            if 'heading' in style_name or '标题' in style_name:
                level = 1  # 默认级别
                if 'heading 1' in style_name or '1级标题' in style_name:
                    level = 1
                elif 'heading 2' in style_name or '2级标题' in style_name:
                    level = 2
                elif 'heading 3' in style_name or '3级标题' in style_name:
                    level = 3
                elif 'heading 4' in style_name or '4级标题' in style_name:
                    level = 4
                
                text = para.text.strip()
                if text:
                    headings.append((level, text))
        
        # 生成目录条目
        for level, title_text in headings:
            toc_entry_para = target_para.insert_paragraph_before()
            
            # 根据级别设置缩进
            indent = (level - 1) * 0.5
            toc_entry_para.paragraph_format.left_indent = Inches(indent)
            
            # 添加标题文本
            toc_run = toc_entry_para.add_run(title_text)
            toc_run.font.size = Pt(12)
            toc_run.font.name = '宋体'
            
            # 设置行间距
            toc_entry_para.paragraph_format.line_spacing = 1.2
    
    def _optimize_document_formatting(self):
        """优化文档格式"""
        for para in self.document.paragraphs:
            # 跳过首页和目录部分的段落
            if not para.text.strip():
                continue
            
            # 优化标题格式
            style_name = para.style.name.lower()
            if 'heading' in style_name or '标题' in style_name:
                for run in para.runs:
                    run.font.name = '宋体'
                    if 'heading 1' in style_name:
                        run.font.size = Pt(18)
                    elif 'heading 2' in style_name:
                        run.font.size = Pt(16)
                    elif 'heading 3' in style_name:
                        run.font.size = Pt(14)
                    else:
                        run.font.size = Pt(13)
                    run.font.bold = True
                
                # 设置段落格式
                para.paragraph_format.space_before = Pt(12)
                para.paragraph_format.space_after = Pt(6)
            
            # 优化正文格式
            elif 'normal' in style_name or '正文' in style_name:
                for run in para.runs:
                    run.font.name = '宋体'
                    run.font.size = Pt(12)
                
                # 设置段落格式
                para.paragraph_format.line_spacing = 1.5
                para.paragraph_format.space_after = Pt(6)
                para.paragraph_format.first_line_indent = Inches(0.5)
    
    def get_document_info(self, source_path):
        """获取文档信息"""
        if not os.path.exists(source_path):
            return None
        
        try:
            doc = Document(source_path)
            
            # 统计各种元素
            paragraphs = len(doc.paragraphs)
            tables = len(doc.tables)
            
            # 统计图片（简化检测）
            images = 0
            for para in doc.paragraphs:
                for run in para.runs:
                    if run._element.xpath('.//a:blip'):
                        images += 1
            
            # 统计标题
            headings = []
            for para in doc.paragraphs:
                style_name = para.style.name.lower()
                if 'heading' in style_name and para.text.strip():
                    headings.append(para.text.strip())
            
            return {
                'paragraphs': paragraphs,
                'tables': tables,
                'images': images,
                'headings': headings,
                'file_size': os.path.getsize(source_path)
            }
            
        except Exception as e:
            return {'error': str(e)}
