#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查英文摘要结构
"""

from docx import Document

def check_english_abstract():
    """检查源文件中的英文摘要结构"""
    
    source_doc = Document('testdoc.docx')
    
    print('=== 检查英文摘要结构（Abstract到Keywords）===')
    
    # 查找英文摘要范围
    abstract_start = -1
    abstract_end = -1
    
    for i, para in enumerate(source_doc.paragraphs):
        text = para.text.strip()
        if text:
            # 查找Abstract标题
            if 'Abstract' in text and abstract_start == -1:
                print(f'找到英文摘要开始: 第{i+1}段 - "{text}"')
                abstract_start = i
            # 查找Keywords（英文摘要结束）
            elif abstract_start != -1 and 'Keywords' in text:
                print(f'找到英文摘要结束: 第{i+1}段 - "{text[:50]}..."')
                abstract_end = i
                break
    
    print(f'\n英文摘要区间: 第{abstract_start+1}段 到 第{abstract_end+1}段')
    
    if abstract_start != -1 and abstract_end != -1:
        print(f'\n英文摘要区间内容（共{abstract_end - abstract_start + 1}段）:')
        for i in range(abstract_start, abstract_end + 1):
            text = source_doc.paragraphs[i].text.strip()
            if text:
                print(f'第{i+1}段: {text[:100]}...')
    else:
        print('\n❌ 未找到完整的英文摘要区间')
    
    # 检查中英文摘要的位置关系
    print('\n=== 中英文摘要位置关系 ===')
    
    # 查找中文摘要
    chinese_abstract_start = -1
    chinese_abstract_end = -1
    
    for i, para in enumerate(source_doc.paragraphs):
        text = para.text.strip()
        if text:
            if ('摘要' in text or '摘 要' in text) and chinese_abstract_start == -1:
                chinese_abstract_start = i
            elif chinese_abstract_start != -1 and '关键词' in text:
                chinese_abstract_end = i
                break
    
    print(f'中文摘要区间: 第{chinese_abstract_start+1}段 到 第{chinese_abstract_end+1}段')
    print(f'英文摘要区间: 第{abstract_start+1}段 到 第{abstract_end+1}段')
    
    if chinese_abstract_end != -1 and abstract_start != -1:
        gap = abstract_start - chinese_abstract_end
        print(f'中英文摘要间隔: {gap}段')

if __name__ == "__main__":
    check_english_abstract()
