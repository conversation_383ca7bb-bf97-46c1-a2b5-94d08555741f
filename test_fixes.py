#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试修复后的功能：诚信声明签名和目录结构
"""

from thesis_formatter_v3 import ThesisFormatterV3
from docx import Document

def test_fixes():
    """测试修复后的功能"""
    
    print('=== 测试修复后的功能 ===')
    
    formatter = ThesisFormatterV3()
    
    try:
        output_path = formatter.format_thesis_document(
            source_path='testdoc.docx',
            thesis_title='基于人工智能的文档处理系统设计与实现',
            student_name='修复测试',
            student_id='2021001251',
            department='智能科学与技术系',
            major='人工智能技术应用',
            class_name='AI2101班',
            supervisor='修复教授',
            supervisor_title='教授',
            enterprise_supervisor='修复工程师',
            thesis_date='二○二五年七月',
            school_name='江西泰豪动漫职业学院',
            year='2025'
        )
        
        print(f'✅ 修复版本生成成功: {output_path}')
        
        # 分析生成的文档
        doc = Document(output_path)
        
        print(f'段落总数: {len(doc.paragraphs)}')
        print(f'表格总数: {len(doc.tables)}')
        
        # 检查诚信声明页的签名部分
        print('\n=== 诚信声明签名检查 ===')
        declaration_found = False
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if '诚信声明' in text:
                declaration_found = True
                print(f'找到诚信声明标题: 第{i+1}段')
                
                # 检查后面的签名部分
                for j in range(i+1, min(i+20, len(doc.paragraphs))):
                    next_text = doc.paragraphs[j].text.strip()
                    if '作  者 签 名' in next_text:
                        print(f'  签名行1: 第{j+1}段 - {next_text}')
                        print(f'  对齐方式: {doc.paragraphs[j].alignment}')
                        if '___' in next_text:
                            print('  ✅ 包含下划线')
                        else:
                            print('  ❌ 缺少下划线')
                    elif '指导教师签名' in next_text:
                        print(f'  签名行2: 第{j+1}段 - {next_text}')
                        print(f'  对齐方式: {doc.paragraphs[j].alignment}')
                        if '___' in next_text:
                            print('  ✅ 包含下划线')
                        else:
                            print('  ❌ 缺少下划线')
                    elif '年    月    日' in next_text:
                        print(f'  日期行: 第{j+1}段 - {next_text}')
                        print(f'  对齐方式: {doc.paragraphs[j].alignment}')
                        break
                break
        
        if not declaration_found:
            print('❌ 未找到诚信声明')
        
        # 检查目录结构
        print('\n=== 目录结构检查 ===')
        toc_found = False
        toc_table = None
        
        # 查找目录表格
        for i, table in enumerate(doc.tables):
            if len(table.rows) > 10:  # 目录表格通常有很多行
                # 检查第一行是否包含目录相关内容
                first_row_text = table.rows[0].cells[0].text.strip()
                if '摘要' in first_row_text or '目录' in first_row_text:
                    toc_table = table
                    toc_found = True
                    print(f'找到目录表格: 表格{i+1} ({len(table.rows)}行)')
                    break
        
        if toc_table:
            print('\n目录条目:')
            chapter_count = 0
            section_count = 0
            subsection_count = 0
            
            for i, row in enumerate(toc_table.rows[:15]):  # 显示前15行
                title = row.cells[0].text.strip()
                page_num = row.cells[1].text.strip()
                
                if title:
                    # 判断级别
                    if ('第' in title and '章' in title) or (title.startswith('1 ') or title.startswith('2 ') or title.startswith('3 ')):
                        level = '章'
                        chapter_count += 1
                    elif title.count('.') == 1 and title[0].isdigit():
                        level = '节'
                        section_count += 1
                    elif title.count('.') == 2 and title[0].isdigit():
                        level = '子节'
                        subsection_count += 1
                    else:
                        level = '其他'
                    
                    print(f'  [{level}] {title} ... {page_num}')
            
            print(f'\n目录统计:')
            print(f'  章数量: {chapter_count}')
            print(f'  节数量: {section_count}')
            print(f'  子节数量: {subsection_count}')
            
            if chapter_count > 0 and section_count > 0:
                print('✅ 目录结构层次正确')
            else:
                print('❌ 目录结构可能有问题')
        else:
            print('❌ 未找到目录表格')
        
        return output_path
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_fixes()
