#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试字体大小配置功能
"""

from thesis_formatter_v3 import ThesisFormatterV3
from docx import Document

def test_font_sizes():
    """测试字体大小配置功能"""
    
    print('=== 测试字体大小配置功能 ===')
    
    formatter = ThesisFormatterV3()
    
    # 测试自定义字体大小
    custom_font_sizes = {
        'cover_main_title_size': 48,  # 更大的封面标题
        'cover_thesis_title_size': 24,  # 更大的论文题目
        'cover_info_size': 16,  # 更大的学生信息
        'section_title_size': 18,  # 更大的部分标题
        'body_text_size': 14,  # 更大的正文
        'chinese_abstract_size': 13,  # 稍大的中文摘要
        'english_abstract_size': 13,  # 稍大的英文摘要
        'toc_content_size': 13,  # 稍大的目录
        'table_text_size': 11,  # 稍大的表格文字
        'header_footer_size': 9,  # 稍小的页眉页脚
    }
    
    try:
        output_path = formatter.format_thesis_document(
            source_path='testdoc.docx',
            thesis_title='基于人工智能的文档处理系统设计与实现',
            student_name='字体测试',
            student_id='2021001248',
            department='智能科学与技术系',
            major='人工智能技术应用',
            class_name='AI2101班',
            supervisor='字体教授',
            supervisor_title='教授',
            enterprise_supervisor='字体工程师',
            thesis_date='二○二五年七月',
            school_name='江西泰豪动漫职业学院',
            year='2025',
            **custom_font_sizes  # 传递自定义字体大小
        )
        
        print(f'✅ 自定义字体大小版本生成成功: {output_path}')
        
        # 分析生成的文档
        doc = Document(output_path)
        
        print(f'段落总数: {len(doc.paragraphs)}')
        print(f'表格总数: {len(doc.tables)}')
        
        # 检查字体大小是否正确应用
        print('\n=== 字体大小检查 ===')
        
        # 检查封面标题
        for i, para in enumerate(doc.paragraphs[:10]):
            text = para.text.strip()
            if '毕业论文' in text and len(text) <= 10:
                if para.runs:
                    font_size = para.runs[0].font.size
                    if font_size:
                        print(f'封面主标题字体大小: {font_size.pt}pt (期望: 48pt)')
                break
        
        # 检查论文题目
        for i, para in enumerate(doc.paragraphs[:15]):
            text = para.text.strip()
            if '基于人工智能' in text:
                if para.runs:
                    font_size = para.runs[0].font.size
                    if font_size:
                        print(f'论文题目字体大小: {font_size.pt}pt (期望: 24pt)')
                break
        
        # 检查摘要标题
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if '摘 要' in text or '摘要' in text:
                if para.runs:
                    font_size = para.runs[0].font.size
                    if font_size:
                        print(f'摘要标题字体大小: {font_size.pt}pt (期望: 18pt)')
                break
        
        # 检查英文摘要标题
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if 'Abstract' in text and len(text) <= 20:
                if para.runs:
                    font_size = para.runs[0].font.size
                    if font_size:
                        print(f'英文摘要标题字体大小: {font_size.pt}pt (期望: 18pt)')
                break
        
        # 检查正文内容
        content_found = False
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if '正文内容' in text:
                content_found = True
                # 检查正文后面的段落
                for j in range(i+2, min(i+10, len(doc.paragraphs))):
                    next_para = doc.paragraphs[j]
                    if next_para.text.strip() and next_para.runs:
                        font_size = next_para.runs[0].font.size
                        if font_size:
                            print(f'正文字体大小: {font_size.pt}pt (期望: 14pt)')
                            break
                break
        
        print('\n✅ 字体大小配置功能测试完成')
        return output_path
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
        return None

def test_default_font_sizes():
    """测试默认字体大小"""
    
    print('\n=== 测试默认字体大小 ===')
    
    formatter = ThesisFormatterV3()
    
    try:
        output_path = formatter.format_thesis_document(
            source_path='testdoc.docx',
            thesis_title='基于人工智能的文档处理系统设计与实现',
            student_name='默认字体',
            student_id='2021001249',
            department='智能科学与技术系',
            major='人工智能技术应用',
            class_name='AI2101班',
            supervisor='默认教授',
            supervisor_title='教授',
            enterprise_supervisor='默认工程师',
            thesis_date='二○二五年七月',
            school_name='江西泰豪动漫职业学院',
            year='2025'
            # 不传递字体大小参数，使用默认值
        )
        
        print(f'✅ 默认字体大小版本生成成功: {output_path}')
        return output_path
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        return None

if __name__ == "__main__":
    # 测试自定义字体大小
    custom_doc = test_font_sizes()
    
    # 测试默认字体大小
    default_doc = test_default_font_sizes()
    
    print('\n=== 测试总结 ===')
    if custom_doc and default_doc:
        print('✅ 字体大小配置功能完全正常')
        print('✅ 默认字体大小功能完全正常')
        print('🎉 字体大小配置功能测试成功！')
    else:
        print('❌ 部分测试失败')
