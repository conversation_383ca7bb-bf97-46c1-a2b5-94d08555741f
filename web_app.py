#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Word文档生成器Web服务
提供RESTful API接口和Web界面
"""

from flask import Flask, request, jsonify, send_file, render_template_string
from flask_cors import CORS
import os
import tempfile
from datetime import datetime
from word_generator import WordDocumentGenerator
from document_reformatter import DocumentReformatter
from thesis_formatter import ThesisFormatter
from thesis_formatter_v2 import ThesisFormatterV2
from thesis_formatter_v3 import ThesisFormatterV3
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 配置文件夹
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word文档自动排版系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 300px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .file-upload {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .file-upload:hover {
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Word文档自动排版系统</h1>
        
        <div class="tabs">
            <div class="tab active" onclick="showTab('generate')">生成文档</div>
            <div class="tab" onclick="showTab('reformat')">智能重排版</div>
            <div class="tab" onclick="showTab('thesis')">毕业论文格式</div>
            <div class="tab" onclick="showTab('extract')">提取内容</div>
            <div class="tab" onclick="showTab('files')">文件管理</div>
        </div>
        
        <!-- 生成文档标签页 -->
        <div id="generate" class="tab-content active">
            <form id="documentForm">
                <div class="form-group">
                    <label>文档标题:</label>
                    <input type="text" id="title" value="测试文档" required>
                </div>
                
                <div class="form-group">
                    <label>作者:</label>
                    <input type="text" id="author" value="张三">
                </div>
                
                <div class="form-group">
                    <label>公司名称:</label>
                    <input type="text" id="company" value="某某科技有限公司">
                </div>
                
                <div class="form-group">
                    <label>文档内容 (支持Markdown格式标题、表格、图片、公式等复杂元素):</label>
                    <textarea id="content" required># 第一章 引言

这是第一章的内容，介绍整个文档的背景和目的。

## 1.1 研究背景

在这个快速发展的时代，文档处理变得越来越重要。

### 1.1.1 技术发展

人工智能和机器学习技术的进步为文档处理带来了新的可能性。

## 1.2 研究目标

本研究的主要目标是开发一个自动化的文档排版系统。

# 第二章 技术方案

本章详细介绍技术实现方案。

## 2.1 系统架构

系统采用模块化设计，包含以下主要组件：

1. 文档解析模块
2. 格式处理模块
3. 输出生成模块

## 2.2 核心技术

### 2.2.1 Python-docx库

使用python-docx库进行Word文档的生成和格式化。

### 2.2.2 Flask框架

使用Flask提供Web服务接口。

# 第三章 结论

## 3.1 总结

本项目成功开发了一个功能完整的Word文档自动排版系统。

## 3.2 未来工作

未来将继续优化系统性能，增加更多功能特性。</textarea>
                </div>
                
                <div class="form-group">
                    <label>页眉文本:</label>
                    <input type="text" id="header_text" value="文档页眉">
                </div>
                
                <div class="form-group">
                    <label>页脚文本 (使用{page}表示页码):</label>
                    <input type="text" id="footer_text" value="第 {page} 页">
                </div>
                
                <button type="submit">生成Word文档</button>
            </form>
        </div>

        <!-- 智能重排版标签页 -->
        <div id="reformat" class="tab-content">
            <div style="background-color: #e8f5e8; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #006600;">🚀 智能重排版功能 - 推荐使用</h4>
                <p style="margin-bottom: 10px;"><strong>基于原文档直接修改，100%保留所有原始元素：</strong></p>
                <ul style="margin-bottom: 0;">
                    <li>✅ <strong>完整保留</strong>：图片、表格、公式、图表等所有元素</li>
                    <li>✅ <strong>智能排版</strong>：添加首页、目录、页眉页脚</li>
                    <li>✅ <strong>格式优化</strong>：统一字体、间距、样式</li>
                    <li>✅ <strong>无损处理</strong>：基于原文档直接修改，不会丢失任何内容</li>
                </ul>
            </div>

            <form id="reformatForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label>上传Word文档 (.docx):</label>
                    <input type="file" id="reformatFile" name="file" accept=".docx" required>
                </div>

                <div class="form-group">
                    <label>文档标题:</label>
                    <input type="text" id="reformatTitle" name="title" value="智能重排版文档" required>
                </div>

                <div class="form-group">
                    <label>作者:</label>
                    <input type="text" id="reformatAuthor" name="author" value="智能处理系统">
                </div>

                <div class="form-group">
                    <label>公司名称:</label>
                    <input type="text" id="reformatCompany" name="company" value="文档处理中心">
                </div>

                <div class="form-group">
                    <label>首页标题:</label>
                    <input type="text" id="reformatFirstPageTitle" name="first_page_title" placeholder="默认使用文档标题">
                </div>

                <div class="form-group">
                    <label>首页副标题:</label>
                    <input type="text" id="reformatFirstPageSubtitle" name="first_page_subtitle" value="智能重排版 | 保留所有原始元素">
                </div>

                <div class="form-group">
                    <label>页眉文本:</label>
                    <input type="text" id="reformatHeaderText" name="header_text" value="智能重排版文档">
                </div>

                <div class="form-group">
                    <label>页脚文本 (使用{page}表示页码):</label>
                    <input type="text" id="reformatFooterText" name="footer_text" value="第 {page} 页">
                </div>

                <div class="form-group">
                    <label>
                        <input type="checkbox" id="reformatAddToc" name="add_toc" checked>
                        自动生成目录
                    </label>
                </div>

                <button type="submit">开始智能重排版</button>
            </form>
        </div>

        <!-- 毕业论文格式标签页 -->
        <div id="thesis" class="tab-content">
            <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #856404;">🎓 毕业论文格式化 - 基于jieguo.docx样板</h4>
                <p style="margin-bottom: 10px;"><strong>专门用于毕业论文格式化，完全符合学术规范：</strong></p>
                <ul style="margin-bottom: 0;">
                    <li>✅ <strong>标准封面</strong>：包含论文题目、学生信息、指导教师等</li>
                    <li>✅ <strong>诚信声明</strong>：自动生成毕业论文诚信声明页</li>
                    <li>✅ <strong>摘要页面</strong>：标准的摘要和关键词格式</li>
                    <li>✅ <strong>学术页眉页脚</strong>：符合学校要求的页眉页脚格式</li>
                    <li>✅ <strong>保留原始内容</strong>：完整保留原文档的所有表格、图片等</li>
                </ul>
            </div>

            <form id="thesisForm" enctype="multipart/form-data">
                <div class="form-group">
                    <label>上传Word文档 (.docx):</label>
                    <input type="file" id="thesisFile" name="file" accept=".docx" required>
                </div>

                <div class="form-group">
                    <label>论文题目:</label>
                    <input type="text" id="thesisTitle" name="thesis_title" value="基于人工智能的文档处理系统设计与实现" required>
                </div>

                <div class="form-group">
                    <label>学生姓名:</label>
                    <input type="text" id="studentName" name="student_name" value="" required>
                </div>

                <div class="form-group">
                    <label>学号:</label>
                    <input type="text" id="studentId" name="student_id" value="" required>
                </div>

                <div class="form-group">
                    <label>院系:</label>
                    <input type="text" id="department" name="department" value="智能科学与技术系">
                </div>

                <div class="form-group">
                    <label>专业:</label>
                    <input type="text" id="major" name="major" value="人工智能技术应用">
                </div>

                <div class="form-group">
                    <label>班级:</label>
                    <input type="text" id="className" name="class_name" value="">
                </div>

                <div class="form-group">
                    <label>指导教师:</label>
                    <input type="text" id="supervisor" name="supervisor" value="">
                </div>

                <div class="form-group">
                    <label>职称:</label>
                    <input type="text" id="supervisorTitle" name="supervisor_title" value="副教授">
                </div>

                <div class="form-group">
                    <label>企业导师:</label>
                    <input type="text" id="enterpriseSupervisor" name="enterprise_supervisor" value="">
                </div>

                <div class="form-group">
                    <label>学校名称:</label>
                    <input type="text" id="schoolName" name="school_name" value="江西泰豪动漫职业学院">
                </div>

                <div class="form-group">
                    <label>年份:</label>
                    <input type="text" id="year" name="year" value="2025">
                </div>

                <div class="form-group">
                    <label>论文日期:</label>
                    <input type="text" id="thesisDate" name="thesis_date" value="二○二五年七月">
                </div>

                <!-- 字体大小配置 -->
                <div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin: 20px 0;">
                    <h4 style="margin-top: 0; color: #495057;">🎨 字体大小配置</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div class="form-group">
                            <label>封面主标题字号:</label>
                            <input type="number" name="cover_main_title_size" value="42" min="20" max="60">
                        </div>
                        <div class="form-group">
                            <label>封面论文题目字号:</label>
                            <input type="number" name="cover_thesis_title_size" value="22" min="16" max="30">
                        </div>
                        <div class="form-group">
                            <label>封面信息字号:</label>
                            <input type="number" name="cover_info_size" value="14" min="10" max="18">
                        </div>
                        <div class="form-group">
                            <label>封面日期字号:</label>
                            <input type="number" name="cover_date_size" value="18" min="14" max="24">
                        </div>
                        <div class="form-group">
                            <label>各部分标题字号:</label>
                            <input type="number" name="section_title_size" value="16" min="12" max="20">
                        </div>
                        <div class="form-group">
                            <label>中文摘要字号:</label>
                            <input type="number" name="chinese_abstract_size" value="12" min="10" max="16">
                        </div>
                        <div class="form-group">
                            <label>英文摘要字号:</label>
                            <input type="number" name="english_abstract_size" value="12" min="10" max="16">
                        </div>
                        <div class="form-group">
                            <label>目录内容字号:</label>
                            <input type="number" name="toc_content_size" value="12" min="10" max="16">
                        </div>
                        <div class="form-group">
                            <label>正文字号:</label>
                            <input type="number" name="body_text_size" value="12" min="10" max="16">
                        </div>
                        <div class="form-group">
                            <label>表格文字字号:</label>
                            <input type="number" name="table_text_size" value="10" min="8" max="14">
                        </div>
                        <div class="form-group">
                            <label>页眉页脚字号:</label>
                            <input type="number" name="header_footer_size" value="10" min="8" max="14">
                        </div>
                    </div>
                    <div style="margin-top: 10px; padding: 10px; background-color: #e9ecef; border-radius: 3px;">
                        <small><strong>提示：</strong>字体大小单位为磅（pt），建议保持默认值以确保最佳效果。</small>
                    </div>
                </div>

                <button type="submit">生成毕业论文格式</button>
            </form>
        </div>

        <!-- 提取内容标签页 -->
        <div id="extract" class="tab-content">
            <div style="background-color: #e7f3ff; padding: 15px; border-radius: 5px; margin-bottom: 20px;">
                <h4 style="margin-top: 0; color: #0066cc;">📋 复杂元素处理功能</h4>
                <p style="margin-bottom: 10px;">系统支持从Word文档中提取和重排版以下元素：</p>
                <ul style="margin-bottom: 0;">
                    <li>✅ <strong>表格</strong>：完整保留表格结构和数据</li>
                    <li>✅ <strong>图片</strong>：提取图片位置信息（占位符处理）</li>
                    <li>✅ <strong>公式</strong>：识别数学公式（占位符处理）</li>
                    <li>✅ <strong>多级标题</strong>：自动转换为Markdown格式</li>
                    <li>✅ <strong>格式化文本</strong>：保持段落结构和样式</li>
                </ul>
            </div>
            <div class="file-upload" onclick="document.getElementById('fileInput').click()">
                <p>点击选择或拖拽.docx文件到此处</p>
                <p style="font-size: 12px; color: #666;">支持提取表格、图片、公式等复杂元素</p>
                <input type="file" id="fileInput" accept=".docx" style="display: none;" onchange="extractContent()">
            </div>
            <div id="extractedContent" style="display: none;">
                <h3>提取的内容:</h3>
                <textarea id="extractedText" readonly style="height: 400px;"></textarea>
                <button onclick="useExtractedContent()">使用此内容生成新文档</button>
            </div>
        </div>
        
        <!-- 文件管理标签页 -->
        <div id="files" class="tab-content">
            <h3>已生成的文档</h3>
            <button onclick="loadFileList()">刷新列表</button>
            <div id="fileList"></div>
        </div>
        
        <div id="status" class="status"></div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // 如果是文件管理标签页，自动加载文件列表
            if (tabName === 'files') {
                loadFileList();
            }
        }
        
        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + (isError ? 'error' : 'success');
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 5000);
        }
        
        document.getElementById('documentForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const data = {
                content: document.getElementById('content').value,
                title: document.getElementById('title').value,
                author: document.getElementById('author').value,
                company: document.getElementById('company').value,
                page_settings: {
                    header_text: document.getElementById('header_text').value,
                    footer_text: document.getElementById('footer_text').value
                }
            };

            try {
                showStatus('正在生成文档...');

                const response = await fetch('/generate-document', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = data.title + '.docx';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showStatus('文档生成成功！');
                } else {
                    const error = await response.json();
                    showStatus('生成失败: ' + error.error, true);
                }
            } catch (error) {
                showStatus('网络错误: ' + error.message, true);
            }
        });

        // 智能重排版表单处理
        document.getElementById('reformatForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            // 检查文件
            const fileInput = document.getElementById('reformatFile');
            if (!fileInput.files[0]) {
                showStatus('请选择要重排版的Word文档', true);
                return;
            }

            try {
                showStatus('正在进行智能重排版，请稍候...');

                const response = await fetch('/reformat-document', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = formData.get('title') + '_重排版.docx';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showStatus('智能重排版完成！文档已下载');
                } else {
                    const error = await response.json();
                    showStatus('重排版失败: ' + error.error, true);
                }
            } catch (error) {
                showStatus('网络错误: ' + error.message, true);
            }
        });

        // 毕业论文格式化表单处理
        document.getElementById('thesisForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const formData = new FormData(this);

            // 检查文件
            const fileInput = document.getElementById('thesisFile');
            if (!fileInput.files[0]) {
                showStatus('请选择要格式化的Word文档', true);
                return;
            }

            // 检查必填字段
            const studentName = document.getElementById('studentName').value.trim();
            const studentId = document.getElementById('studentId').value.trim();
            if (!studentName || !studentId) {
                showStatus('请填写学生姓名和学号', true);
                return;
            }

            try {
                showStatus('正在生成毕业论文格式，请稍候...');

                const response = await fetch('/format-thesis', {
                    method: 'POST',
                    body: formData
                });

                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = '毕业论文_' + studentName + '.docx';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);

                    showStatus('毕业论文格式化完成！文档已下载');
                } else {
                    const error = await response.json();
                    showStatus('格式化失败: ' + error.error, true);
                }
            } catch (error) {
                showStatus('网络错误: ' + error.message, true);
            }
        });

        async function extractContent() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) return;
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                showStatus('正在提取内容...');
                
                const response = await fetch('/extract-content', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('extractedText').value = result.content;
                    document.getElementById('extractedContent').style.display = 'block';
                    showStatus('内容提取成功！');
                } else {
                    showStatus('提取失败: ' + result.error, true);
                }
            } catch (error) {
                showStatus('网络错误: ' + error.message, true);
            }
        }
        
        function useExtractedContent() {
            const extractedText = document.getElementById('extractedText').value;
            document.getElementById('content').value = extractedText;
            showTab('generate');
            showStatus('已将提取的内容填入生成表单');
        }
        
        async function loadFileList() {
            try {
                const response = await fetch('/list-outputs');
                const result = await response.json();
                
                const fileList = document.getElementById('fileList');
                
                if (result.success && result.files.length > 0) {
                    let html = '<table style="width: 100%; border-collapse: collapse; margin-top: 10px;">';
                    html += '<tr style="background-color: #f8f9fa;"><th style="padding: 10px; border: 1px solid #ddd;">文件名</th><th style="padding: 10px; border: 1px solid #ddd;">大小</th><th style="padding: 10px; border: 1px solid #ddd;">创建时间</th><th style="padding: 10px; border: 1px solid #ddd;">操作</th></tr>';
                    
                    result.files.forEach(file => {
                        const size = (file.size / 1024).toFixed(1) + ' KB';
                        const time = new Date(file.created_time).toLocaleString();
                        html += `<tr>
                            <td style="padding: 10px; border: 1px solid #ddd;">${file.filename}</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">${size}</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">${time}</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">
                                <button onclick="downloadFile('${file.filename}')" style="margin-right: 5px;">下载</button>
                                <button onclick="deleteFile('${file.filename}')" style="background-color: #dc3545;">删除</button>
                            </td>
                        </tr>`;
                    });
                    
                    html += '</table>';
                    fileList.innerHTML = html;
                } else {
                    fileList.innerHTML = '<p>暂无生成的文档</p>';
                }
            } catch (error) {
                showStatus('加载文件列表失败: ' + error.message, true);
            }
        }
        
        function downloadFile(filename) {
            window.open('/download/' + encodeURIComponent(filename));
        }
        
        async function deleteFile(filename) {
            if (!confirm('确定要删除文件 ' + filename + ' 吗？')) {
                return;
            }
            
            try {
                const response = await fetch('/delete/' + encodeURIComponent(filename), {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus('文件删除成功');
                    loadFileList();
                } else {
                    showStatus('删除失败: ' + result.error, true);
                }
            } catch (error) {
                showStatus('网络错误: ' + error.message, true);
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'Word Document Generator API',
        'version': '1.0.0'
    })

@app.route('/generate-document', methods=['POST'])
def generate_document():
    """生成Word文档"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400
        
        # 提取基本参数
        content = data.get('content', '')
        if not content.strip():
            return jsonify({'error': '文档内容不能为空'}), 400
            
        title = data.get('title', '文档标题')
        author = data.get('author', '作者')
        company = data.get('company', '公司名称')
        
        # 页面设置
        page_settings = data.get('page_settings', {})
        header_text = page_settings.get('header_text', '文档页眉')
        footer_text = page_settings.get('footer_text', '第 {page} 页')
        first_page_header = page_settings.get('first_page_header', '首页页眉')
        first_page_footer = page_settings.get('first_page_footer', '首页页脚')
        
        # 样式设置
        style_settings = data.get('style_settings', {})
        title_font_size = style_settings.get('title_font_size', 16)
        heading_font_size = style_settings.get('heading_font_size', 14)
        body_font_size = style_settings.get('body_font_size', 12)
        line_spacing = style_settings.get('line_spacing', 1.5)
        paragraph_spacing = style_settings.get('paragraph_spacing', 12)
        
        # 首页设置
        first_page_settings = data.get('first_page_settings', {})
        first_page_title = first_page_settings.get('title', title)
        first_page_subtitle = first_page_settings.get('subtitle', '副标题')
        first_page_author = first_page_settings.get('author', author)
        first_page_date = first_page_settings.get('date', datetime.now().strftime('%Y年%m月%d日'))
        
        # 生成文档
        generator = WordDocumentGenerator()
        doc_path = generator.generate_document(
            content=content,
            title=title,
            author=author,
            company=company,
            header_text=header_text,
            footer_text=footer_text,
            first_page_header=first_page_header,
            first_page_footer=first_page_footer,
            title_font_size=title_font_size,
            heading_font_size=heading_font_size,
            body_font_size=body_font_size,
            line_spacing=line_spacing,
            paragraph_spacing=paragraph_spacing,
            first_page_title=first_page_title,
            first_page_subtitle=first_page_subtitle,
            first_page_author=first_page_author,
            first_page_date=first_page_date,
            output_dir=OUTPUT_FOLDER
        )
        
        # 返回文件
        return send_file(
            doc_path,
            as_attachment=True,
            download_name=f"{title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx",
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        
    except Exception as e:
        logger.error(f"生成文档时发生错误: {str(e)}")
        return jsonify({'error': f'生成文档失败: {str(e)}'}), 500

@app.route('/extract-content', methods=['POST'])
def extract_content():
    """从上传的docx文件提取内容"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        if not file.filename.lower().endswith('.docx'):
            return jsonify({'error': '只支持.docx格式文件'}), 400

        # 保存上传的文件
        filename = f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        file.save(filepath)

        # 提取内容
        generator = WordDocumentGenerator()
        content = generator.extract_content_from_docx(filepath)

        # 删除临时文件
        os.remove(filepath)

        if content:
            return jsonify({
                'success': True,
                'content': content,
                'message': '内容提取成功'
            })
        else:
            return jsonify({'error': '无法提取文档内容'}), 400

    except Exception as e:
        logger.error(f"提取内容时发生错误: {str(e)}")
        return jsonify({'error': f'提取内容失败: {str(e)}'}), 500

@app.route('/reformat-document', methods=['POST'])
def reformat_document():
    """直接修改重排版文档 - 保留所有原始元素"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        if not file.filename.lower().endswith('.docx'):
            return jsonify({'error': '只支持.docx格式文件'}), 400

        # 获取表单数据
        title = request.form.get('title', '重排版文档')
        author = request.form.get('author', '作者')
        company = request.form.get('company', '公司名称')
        header_text = request.form.get('header_text', '文档页眉')
        footer_text = request.form.get('footer_text', '第 {page} 页')
        first_page_header = request.form.get('first_page_header', '首页页眉')
        first_page_footer = request.form.get('first_page_footer', '首页页脚')
        first_page_title = request.form.get('first_page_title', title)
        first_page_subtitle = request.form.get('first_page_subtitle', '副标题')
        first_page_author = request.form.get('first_page_author', author)
        add_toc = request.form.get('add_toc', 'true').lower() == 'true'

        # 保存上传的文件
        filename = f"source_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        file.save(filepath)

        # 执行直接修改重排版
        reformatter = DocumentReformatter()
        doc_path = reformatter.reformat_document(
            source_path=filepath,
            title=title,
            author=author,
            company=company,
            header_text=header_text,
            footer_text=footer_text,
            first_page_header=first_page_header,
            first_page_footer=first_page_footer,
            first_page_title=first_page_title,
            first_page_subtitle=first_page_subtitle,
            first_page_author=first_page_author,
            add_toc=add_toc,
            output_dir=OUTPUT_FOLDER
        )

        # 删除临时文件
        os.remove(filepath)

        # 返回文件
        return send_file(
            doc_path,
            as_attachment=True,
            download_name=f"{title}_重排版_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx",
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except Exception as e:
        logger.error(f"重排版文档时发生错误: {str(e)}")
        return jsonify({'error': f'重排版失败: {str(e)}'}), 500

@app.route('/format-thesis', methods=['POST'])
def format_thesis():
    """毕业论文格式化 - 基于jieguo.docx样板"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        if not file.filename.lower().endswith('.docx'):
            return jsonify({'error': '只支持.docx格式文件'}), 400

        # 获取表单数据
        thesis_title = request.form.get('thesis_title', '毕业论文题目')
        student_name = request.form.get('student_name', '')
        student_id = request.form.get('student_id', '')
        department = request.form.get('department', '智能科学与技术系')
        major = request.form.get('major', '')
        class_name = request.form.get('class_name', '')
        supervisor = request.form.get('supervisor', '')
        supervisor_title = request.form.get('supervisor_title', '副教授')
        enterprise_supervisor = request.form.get('enterprise_supervisor', '')
        school_name = request.form.get('school_name', '江西泰豪动漫职业学院')
        year = request.form.get('year', '2025')
        thesis_date = request.form.get('thesis_date', '二○二五年七月')

        # 获取字体大小参数
        font_size_params = {}
        font_size_fields = [
            'cover_main_title_size', 'cover_thesis_title_size', 'cover_info_size', 'cover_date_size',
            'section_title_size', 'chinese_abstract_size', 'english_abstract_size', 'toc_content_size',
            'body_text_size', 'table_text_size', 'header_footer_size'
        ]

        for field in font_size_fields:
            value = request.form.get(field)
            if value:
                try:
                    font_size_params[field] = int(value)
                except ValueError:
                    pass  # 使用默认值

        # 保存上传的文件
        filename = f"thesis_source_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        file.save(filepath)

        # 执行毕业论文格式化（使用V3版本）
        formatter = ThesisFormatterV3()

        # 合并所有参数
        format_params = {
            'source_path': filepath,
            'thesis_title': thesis_title,
            'student_name': student_name,
            'student_id': student_id,
            'department': department,
            'major': major,
            'class_name': class_name,
            'supervisor': supervisor,
            'supervisor_title': supervisor_title,
            'enterprise_supervisor': enterprise_supervisor,
            'thesis_date': thesis_date,
            'school_name': school_name,
            'year': year,
            'output_dir': OUTPUT_FOLDER,
            **font_size_params  # 添加字体大小参数
        }

        doc_path = formatter.format_thesis_document(**format_params)

        # 删除临时文件
        os.remove(filepath)

        # 返回文件
        return send_file(
            doc_path,
            as_attachment=True,
            download_name=f"毕业论文_{student_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx",
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except Exception as e:
        logger.error(f"毕业论文格式化时发生错误: {str(e)}")
        return jsonify({'error': f'毕业论文格式化失败: {str(e)}'}), 500

@app.route('/list-outputs', methods=['GET'])
def list_outputs():
    """列出已生成的文档"""
    try:
        files = []
        if os.path.exists(OUTPUT_FOLDER):
            for filename in os.listdir(OUTPUT_FOLDER):
                if filename.endswith('.docx'):
                    filepath = os.path.join(OUTPUT_FOLDER, filename)
                    stat = os.stat(filepath)
                    files.append({
                        'filename': filename,
                        'size': stat.st_size,
                        'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })

        # 按修改时间排序
        files.sort(key=lambda x: x['modified_time'], reverse=True)

        return jsonify({
            'success': True,
            'files': files,
            'count': len(files)
        })

    except Exception as e:
        logger.error(f"列出文档时发生错误: {str(e)}")
        return jsonify({'error': f'列出文档失败: {str(e)}'}), 500

@app.route('/download/<filename>', methods=['GET'])
def download_file(filename):
    """下载指定文件"""
    try:
        filepath = os.path.join(OUTPUT_FOLDER, filename)
        if not os.path.exists(filepath):
            return jsonify({'error': '文件不存在'}), 404

        return send_file(
            filepath,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except Exception as e:
        logger.error(f"下载文件时发生错误: {str(e)}")
        return jsonify({'error': f'下载文件失败: {str(e)}'}), 500

@app.route('/delete/<filename>', methods=['DELETE'])
def delete_file(filename):
    """删除指定文件"""
    try:
        filepath = os.path.join(OUTPUT_FOLDER, filename)
        if not os.path.exists(filepath):
            return jsonify({'error': '文件不存在'}), 404

        os.remove(filepath)
        return jsonify({
            'success': True,
            'message': f'文件 {filename} 已删除'
        })

    except Exception as e:
        logger.error(f"删除文件时发生错误: {str(e)}")
        return jsonify({'error': f'删除文件失败: {str(e)}'}), 500

if __name__ == '__main__':
    print("启动Word文档生成器Web服务...")
    print("服务地址: http://localhost:5000")
    print("API文档: http://localhost:5000/health")
    app.run(debug=True, host='0.0.0.0', port=5000)
