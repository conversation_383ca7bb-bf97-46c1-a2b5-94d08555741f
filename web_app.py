#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Word文档生成器Web服务
提供RESTful API接口和Web界面
"""

from flask import Flask, request, jsonify, send_file, render_template_string
from flask_cors import CORS
import os
import tempfile
from datetime import datetime
from word_generator import WordDocumentGenerator
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# 配置文件夹
UPLOAD_FOLDER = 'uploads'
OUTPUT_FOLDER = 'outputs'
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs(OUTPUT_FOLDER, exist_ok=True)

# HTML模板
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word文档自动排版系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
            box-sizing: border-box;
        }
        textarea {
            height: 300px;
            resize: vertical;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin-right: 10px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .tabs {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border-bottom: 2px solid transparent;
        }
        .tab.active {
            border-bottom-color: #007bff;
            color: #007bff;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        .file-upload {
            border: 2px dashed #ddd;
            padding: 20px;
            text-align: center;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .file-upload:hover {
            border-color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Word文档自动排版系统</h1>
        
        <div class="tabs">
            <div class="tab active" onclick="showTab('generate')">生成文档</div>
            <div class="tab" onclick="showTab('extract')">提取内容</div>
            <div class="tab" onclick="showTab('files')">文件管理</div>
        </div>
        
        <!-- 生成文档标签页 -->
        <div id="generate" class="tab-content active">
            <form id="documentForm">
                <div class="form-group">
                    <label>文档标题:</label>
                    <input type="text" id="title" value="测试文档" required>
                </div>
                
                <div class="form-group">
                    <label>作者:</label>
                    <input type="text" id="author" value="张三">
                </div>
                
                <div class="form-group">
                    <label>公司名称:</label>
                    <input type="text" id="company" value="某某科技有限公司">
                </div>
                
                <div class="form-group">
                    <label>文档内容 (支持Markdown格式标题):</label>
                    <textarea id="content" required># 第一章 引言

这是第一章的内容，介绍整个文档的背景和目的。

## 1.1 研究背景

在这个快速发展的时代，文档处理变得越来越重要。

### 1.1.1 技术发展

人工智能和机器学习技术的进步为文档处理带来了新的可能性。

## 1.2 研究目标

本研究的主要目标是开发一个自动化的文档排版系统。

# 第二章 技术方案

本章详细介绍技术实现方案。

## 2.1 系统架构

系统采用模块化设计，包含以下主要组件：

1. 文档解析模块
2. 格式处理模块
3. 输出生成模块

## 2.2 核心技术

### 2.2.1 Python-docx库

使用python-docx库进行Word文档的生成和格式化。

### 2.2.2 Flask框架

使用Flask提供Web服务接口。

# 第三章 结论

## 3.1 总结

本项目成功开发了一个功能完整的Word文档自动排版系统。

## 3.2 未来工作

未来将继续优化系统性能，增加更多功能特性。</textarea>
                </div>
                
                <div class="form-group">
                    <label>页眉文本:</label>
                    <input type="text" id="header_text" value="文档页眉">
                </div>
                
                <div class="form-group">
                    <label>页脚文本 (使用{page}表示页码):</label>
                    <input type="text" id="footer_text" value="第 {page} 页">
                </div>
                
                <button type="submit">生成Word文档</button>
            </form>
        </div>
        
        <!-- 提取内容标签页 -->
        <div id="extract" class="tab-content">
            <div class="file-upload" onclick="document.getElementById('fileInput').click()">
                <p>点击选择或拖拽.docx文件到此处</p>
                <input type="file" id="fileInput" accept=".docx" style="display: none;" onchange="extractContent()">
            </div>
            <div id="extractedContent" style="display: none;">
                <h3>提取的内容:</h3>
                <textarea id="extractedText" readonly style="height: 400px;"></textarea>
                <button onclick="useExtractedContent()">使用此内容生成新文档</button>
            </div>
        </div>
        
        <!-- 文件管理标签页 -->
        <div id="files" class="tab-content">
            <h3>已生成的文档</h3>
            <button onclick="loadFileList()">刷新列表</button>
            <div id="fileList"></div>
        </div>
        
        <div id="status" class="status"></div>
    </div>

    <script>
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const contents = document.querySelectorAll('.tab-content');
            contents.forEach(content => content.classList.remove('active'));
            
            // 移除所有标签的active类
            const tabs = document.querySelectorAll('.tab');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
            
            // 如果是文件管理标签页，自动加载文件列表
            if (tabName === 'files') {
                loadFileList();
            }
        }
        
        function showStatus(message, isError = false) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = 'status ' + (isError ? 'error' : 'success');
            status.style.display = 'block';
            
            setTimeout(() => {
                status.style.display = 'none';
            }, 5000);
        }
        
        document.getElementById('documentForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const data = {
                content: document.getElementById('content').value,
                title: document.getElementById('title').value,
                author: document.getElementById('author').value,
                company: document.getElementById('company').value,
                page_settings: {
                    header_text: document.getElementById('header_text').value,
                    footer_text: document.getElementById('footer_text').value
                }
            };
            
            try {
                showStatus('正在生成文档...');
                
                const response = await fetch('/generate-document', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(data)
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = data.title + '.docx';
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                    window.URL.revokeObjectURL(url);
                    
                    showStatus('文档生成成功！');
                } else {
                    const error = await response.json();
                    showStatus('生成失败: ' + error.error, true);
                }
            } catch (error) {
                showStatus('网络错误: ' + error.message, true);
            }
        });
        
        async function extractContent() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) return;
            
            const formData = new FormData();
            formData.append('file', file);
            
            try {
                showStatus('正在提取内容...');
                
                const response = await fetch('/extract-content', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (result.success) {
                    document.getElementById('extractedText').value = result.content;
                    document.getElementById('extractedContent').style.display = 'block';
                    showStatus('内容提取成功！');
                } else {
                    showStatus('提取失败: ' + result.error, true);
                }
            } catch (error) {
                showStatus('网络错误: ' + error.message, true);
            }
        }
        
        function useExtractedContent() {
            const extractedText = document.getElementById('extractedText').value;
            document.getElementById('content').value = extractedText;
            showTab('generate');
            showStatus('已将提取的内容填入生成表单');
        }
        
        async function loadFileList() {
            try {
                const response = await fetch('/list-outputs');
                const result = await response.json();
                
                const fileList = document.getElementById('fileList');
                
                if (result.success && result.files.length > 0) {
                    let html = '<table style="width: 100%; border-collapse: collapse; margin-top: 10px;">';
                    html += '<tr style="background-color: #f8f9fa;"><th style="padding: 10px; border: 1px solid #ddd;">文件名</th><th style="padding: 10px; border: 1px solid #ddd;">大小</th><th style="padding: 10px; border: 1px solid #ddd;">创建时间</th><th style="padding: 10px; border: 1px solid #ddd;">操作</th></tr>';
                    
                    result.files.forEach(file => {
                        const size = (file.size / 1024).toFixed(1) + ' KB';
                        const time = new Date(file.created_time).toLocaleString();
                        html += `<tr>
                            <td style="padding: 10px; border: 1px solid #ddd;">${file.filename}</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">${size}</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">${time}</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">
                                <button onclick="downloadFile('${file.filename}')" style="margin-right: 5px;">下载</button>
                                <button onclick="deleteFile('${file.filename}')" style="background-color: #dc3545;">删除</button>
                            </td>
                        </tr>`;
                    });
                    
                    html += '</table>';
                    fileList.innerHTML = html;
                } else {
                    fileList.innerHTML = '<p>暂无生成的文档</p>';
                }
            } catch (error) {
                showStatus('加载文件列表失败: ' + error.message, true);
            }
        }
        
        function downloadFile(filename) {
            window.open('/download/' + encodeURIComponent(filename));
        }
        
        async function deleteFile(filename) {
            if (!confirm('确定要删除文件 ' + filename + ' 吗？')) {
                return;
            }
            
            try {
                const response = await fetch('/delete/' + encodeURIComponent(filename), {
                    method: 'DELETE'
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showStatus('文件删除成功');
                    loadFileList();
                } else {
                    showStatus('删除失败: ' + result.error, true);
                }
            } catch (error) {
                showStatus('网络错误: ' + error.message, true);
            }
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """主页"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/health', methods=['GET'])
def health_check():
    """健康检查"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'Word Document Generator API',
        'version': '1.0.0'
    })

@app.route('/generate-document', methods=['POST'])
def generate_document():
    """生成Word文档"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': '请求数据不能为空'}), 400
        
        # 提取基本参数
        content = data.get('content', '')
        if not content.strip():
            return jsonify({'error': '文档内容不能为空'}), 400
            
        title = data.get('title', '文档标题')
        author = data.get('author', '作者')
        company = data.get('company', '公司名称')
        
        # 页面设置
        page_settings = data.get('page_settings', {})
        header_text = page_settings.get('header_text', '文档页眉')
        footer_text = page_settings.get('footer_text', '第 {page} 页')
        first_page_header = page_settings.get('first_page_header', '首页页眉')
        first_page_footer = page_settings.get('first_page_footer', '首页页脚')
        
        # 样式设置
        style_settings = data.get('style_settings', {})
        title_font_size = style_settings.get('title_font_size', 16)
        heading_font_size = style_settings.get('heading_font_size', 14)
        body_font_size = style_settings.get('body_font_size', 12)
        line_spacing = style_settings.get('line_spacing', 1.5)
        paragraph_spacing = style_settings.get('paragraph_spacing', 12)
        
        # 首页设置
        first_page_settings = data.get('first_page_settings', {})
        first_page_title = first_page_settings.get('title', title)
        first_page_subtitle = first_page_settings.get('subtitle', '副标题')
        first_page_author = first_page_settings.get('author', author)
        first_page_date = first_page_settings.get('date', datetime.now().strftime('%Y年%m月%d日'))
        
        # 生成文档
        generator = WordDocumentGenerator()
        doc_path = generator.generate_document(
            content=content,
            title=title,
            author=author,
            company=company,
            header_text=header_text,
            footer_text=footer_text,
            first_page_header=first_page_header,
            first_page_footer=first_page_footer,
            title_font_size=title_font_size,
            heading_font_size=heading_font_size,
            body_font_size=body_font_size,
            line_spacing=line_spacing,
            paragraph_spacing=paragraph_spacing,
            first_page_title=first_page_title,
            first_page_subtitle=first_page_subtitle,
            first_page_author=first_page_author,
            first_page_date=first_page_date,
            output_dir=OUTPUT_FOLDER
        )
        
        # 返回文件
        return send_file(
            doc_path,
            as_attachment=True,
            download_name=f"{title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx",
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )
        
    except Exception as e:
        logger.error(f"生成文档时发生错误: {str(e)}")
        return jsonify({'error': f'生成文档失败: {str(e)}'}), 500

@app.route('/extract-content', methods=['POST'])
def extract_content():
    """从上传的docx文件提取内容"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': '没有上传文件'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400

        if not file.filename.lower().endswith('.docx'):
            return jsonify({'error': '只支持.docx格式文件'}), 400

        # 保存上传的文件
        filename = f"temp_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        filepath = os.path.join(UPLOAD_FOLDER, filename)
        file.save(filepath)

        # 提取内容
        generator = WordDocumentGenerator()
        content = generator.extract_content_from_docx(filepath)

        # 删除临时文件
        os.remove(filepath)

        if content:
            return jsonify({
                'success': True,
                'content': content,
                'message': '内容提取成功'
            })
        else:
            return jsonify({'error': '无法提取文档内容'}), 400

    except Exception as e:
        logger.error(f"提取内容时发生错误: {str(e)}")
        return jsonify({'error': f'提取内容失败: {str(e)}'}), 500

@app.route('/list-outputs', methods=['GET'])
def list_outputs():
    """列出已生成的文档"""
    try:
        files = []
        if os.path.exists(OUTPUT_FOLDER):
            for filename in os.listdir(OUTPUT_FOLDER):
                if filename.endswith('.docx'):
                    filepath = os.path.join(OUTPUT_FOLDER, filename)
                    stat = os.stat(filepath)
                    files.append({
                        'filename': filename,
                        'size': stat.st_size,
                        'created_time': datetime.fromtimestamp(stat.st_ctime).isoformat(),
                        'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat()
                    })

        # 按修改时间排序
        files.sort(key=lambda x: x['modified_time'], reverse=True)

        return jsonify({
            'success': True,
            'files': files,
            'count': len(files)
        })

    except Exception as e:
        logger.error(f"列出文档时发生错误: {str(e)}")
        return jsonify({'error': f'列出文档失败: {str(e)}'}), 500

@app.route('/download/<filename>', methods=['GET'])
def download_file(filename):
    """下载指定文件"""
    try:
        filepath = os.path.join(OUTPUT_FOLDER, filename)
        if not os.path.exists(filepath):
            return jsonify({'error': '文件不存在'}), 404

        return send_file(
            filepath,
            as_attachment=True,
            download_name=filename,
            mimetype='application/vnd.openxmlformats-officedocument.wordprocessingml.document'
        )

    except Exception as e:
        logger.error(f"下载文件时发生错误: {str(e)}")
        return jsonify({'error': f'下载文件失败: {str(e)}'}), 500

@app.route('/delete/<filename>', methods=['DELETE'])
def delete_file(filename):
    """删除指定文件"""
    try:
        filepath = os.path.join(OUTPUT_FOLDER, filename)
        if not os.path.exists(filepath):
            return jsonify({'error': '文件不存在'}), 404

        os.remove(filepath)
        return jsonify({
            'success': True,
            'message': f'文件 {filename} 已删除'
        })

    except Exception as e:
        logger.error(f"删除文件时发生错误: {str(e)}")
        return jsonify({'error': f'删除文件失败: {str(e)}'}), 500

if __name__ == '__main__':
    print("启动Word文档生成器Web服务...")
    print("服务地址: http://localhost:5000")
    print("API文档: http://localhost:5000/health")
    app.run(debug=True, host='0.0.0.0', port=5000)
