#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试最终修复的V3版本
"""

from thesis_formatter_v3 import ThesisFormatterV3
from docx import Document

def test_final_v3():
    """测试最终修复的V3版本"""
    
    print('=== 测试最终修复的V3版本（正确摘要提取）===')
    
    formatter = ThesisFormatterV3()
    
    try:
        output_path = formatter.format_thesis_document(
            source_path='testdoc.docx',
            thesis_title='基于人工智能的文档处理系统设计与实现',
            student_name='最终修复版',
            student_id='2021001245',
            department='智能科学与技术系',
            major='人工智能技术应用',
            class_name='AI2101班',
            supervisor='最终教授',
            supervisor_title='教授',
            enterprise_supervisor='最终工程师',
            thesis_date='二○二五年七月',
            school_name='江西泰豪动漫职业学院',
            year='2025'
        )
        
        print(f'✅ 最终修复版生成成功: {output_path}')
        
        # 分析生成的文档
        doc = Document(output_path)
        
        print(f'段落总数: {len(doc.paragraphs)}')
        print(f'表格总数: {len(doc.tables)}')
        
        # 检查摘要是否正确提取
        print('\n=== 摘要检查 ===')
        abstract_found = False
        abstract_content_count = 0
        
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if '摘 要' in text or '摘要' in text:
                print(f'摘要标题: 第{i+1}段 - "{text}"')
                abstract_found = True
                # 检查摘要内容
                for j in range(i+1, min(i+10, len(doc.paragraphs))):
                    next_text = doc.paragraphs[j].text.strip()
                    if next_text:
                        if '北京证券交易所' in next_text:
                            print(f'  ✅ 摘要内容: 第{j+1}段 - 北京证券交易所相关内容')
                            abstract_content_count += 1
                        elif '关键词' in next_text:
                            print(f'  ✅ 关键词: 第{j+1}段 - {next_text[:50]}...')
                            break
                break
        
        # 检查正文中的内容
        print('\n=== 正文检查 ===')
        content_found = False
        content_bjse_count = 0
        
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if '正文内容' in text:
                print(f'正文标题: 第{i+1}段')
                content_found = True
                # 统计正文中的北京证券交易所出现次数
                for j in range(i+1, len(doc.paragraphs)):
                    next_text = doc.paragraphs[j].text.strip()
                    if '北京证券交易所' in next_text:
                        content_bjse_count += 1
                break
        
        # 总结
        print(f'\n=== 内容分布检查 ===')
        print(f'摘要中"北京证券交易所"出现次数: {abstract_content_count}')
        print(f'正文中"北京证券交易所"出现次数: {content_bjse_count}')
        
        total_bjse = 0
        for para in doc.paragraphs:
            if '北京证券交易所' in para.text:
                total_bjse += 1
        
        print(f'全文"北京证券交易所"总出现次数: {total_bjse}')
        
        if abstract_content_count == 1 and content_bjse_count > 0:
            print('✅ 摘要提取正确：摘要中1次，正文中多次，符合预期')
        else:
            print('❌ 摘要提取可能有问题')
        
        return output_path
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_final_v3()
