#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Word文档生成器核心类
支持自动生成目录、页眉页脚、首页布局、自动排版等功能
"""

from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
from docx.enum.section import WD_SECTION, WD_ORIENT
from docx.oxml.shared import OxmlElement, qn
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml
import re
import os
from datetime import datetime

class WordDocumentGenerator:
    """Word文档生成器"""

    def __init__(self):
        self.document = None
        self.heading_levels = []
        self.toc_entries = []

    def generate_document(self, content, title="文档标题", author="作者", company="公司名称",
                         header_text="文档页眉", footer_text="第 {page} 页",
                         first_page_header="首页页眉", first_page_footer="首页页脚",
                         title_font_size=16, heading_font_size=14, body_font_size=12,
                         line_spacing=1.5, paragraph_spacing=12,
                         first_page_title="文档标题", first_page_subtitle="副标题",
                         first_page_author="作者", first_page_date=None,
                         output_dir="outputs"):
        """
        生成Word文档

        Args:
            content: 文档内容（支持Markdown格式标题）
            title: 文档标题
            author: 作者
            company: 公司名称
            header_text: 页眉文本
            footer_text: 页脚文本（支持{page}占位符）
            first_page_header: 首页页眉
            first_page_footer: 首页页脚
            title_font_size: 标题字体大小
            heading_font_size: 标题字体大小
            body_font_size: 正文字体大小
            line_spacing: 行间距
            paragraph_spacing: 段落间距
            first_page_title: 首页标题
            first_page_subtitle: 首页副标题
            first_page_author: 首页作者
            first_page_date: 首页日期
            output_dir: 输出目录

        Returns:
            str: 生成的文档路径
        """

        # 创建新文档
        self.document = Document()
        self.heading_levels = []
        self.toc_entries = []

        # 设置页面属性
        self._setup_page_settings()

        # 预处理内容，提取标题信息
        self._extract_headings(content)

        # 创建首页
        self._create_first_page(first_page_title, first_page_subtitle,
                              first_page_author, first_page_date or datetime.now().strftime('%Y年%m月%d日'),
                              company, title_font_size)

        # 添加分页符
        self.document.add_page_break()

        # 设置页眉页脚
        self._setup_headers_footers(header_text, footer_text, first_page_header, first_page_footer)

        # 添加目录
        self._add_table_of_contents()

        # 添加分页符
        self.document.add_page_break()

        # 处理文档内容
        self._process_content(content, title_font_size, heading_font_size, body_font_size,
                            line_spacing, paragraph_spacing)

        # 保存文档
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f'{title}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.docx')
        self.document.save(output_path)

        return output_path

    def _setup_page_settings(self):
        """设置页面属性"""
        sections = self.document.sections
        for section in sections:
            # 设置页边距
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            section.left_margin = Inches(1.25)
            section.right_margin = Inches(1.25)

            # 设置页面方向和大小
            section.orientation = WD_ORIENT.PORTRAIT
            section.page_width = Inches(8.5)
            section.page_height = Inches(11)

    def _extract_headings(self, content):
        """预处理内容，提取标题信息用于生成目录"""
        lines = content.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测标题级别
            heading_match = re.match(r'^(#{1,6})\s+(.+)$', line)
            if heading_match:
                level = len(heading_match.group(1))
                title_text = heading_match.group(2)
                self.toc_entries.append((level, title_text))

    def _create_first_page(self, title, subtitle, author, date, company, title_font_size):
        """创建首页"""
        # 添加主标题
        title_para = self.document.add_paragraph()
        title_run = title_para.add_run(title)
        title_run.font.size = Pt(title_font_size + 8)  # 首页标题更大
        title_run.font.bold = True
        title_run.font.name = '宋体'
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()

        # 添加副标题
        if subtitle and subtitle != title:
            subtitle_para = self.document.add_paragraph()
            subtitle_run = subtitle_para.add_run(subtitle)
            subtitle_run.font.size = Pt(title_font_size + 2)
            subtitle_run.font.name = '宋体'
            subtitle_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 添加空行
            for _ in range(2):
                self.document.add_paragraph()

        # 添加更多空行使内容居中
        for _ in range(8):
            self.document.add_paragraph()

        # 添加作者信息
        author_para = self.document.add_paragraph()
        author_run = author_para.add_run(f"作者：{author}")
        author_run.font.size = Pt(14)
        author_run.font.name = '宋体'
        author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        self.document.add_paragraph()

        # 添加公司信息
        company_para = self.document.add_paragraph()
        company_run = company_para.add_run(company)
        company_run.font.size = Pt(14)
        company_run.font.name = '宋体'
        company_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()

        # 添加日期
        date_para = self.document.add_paragraph()
        date_run = date_para.add_run(date)
        date_run.font.size = Pt(14)
        date_run.font.name = '宋体'
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

    def _setup_headers_footers(self, header_text, footer_text, first_page_header, first_page_footer):
        """设置页眉页脚"""
        section = self.document.sections[0]

        # 设置不同的首页页眉页脚
        section.different_first_page_header_footer = True

        # 首页页眉
        first_header = section.first_page_header
        first_header_para = first_header.paragraphs[0]
        first_header_para.text = first_page_header
        first_header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if first_header_para.runs:
            first_header_run = first_header_para.runs[0]
            first_header_run.font.size = Pt(10)
            first_header_run.font.name = '宋体'

        # 首页页脚
        first_footer = section.first_page_footer
        first_footer_para = first_footer.paragraphs[0]
        first_footer_para.text = first_page_footer
        first_footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if first_footer_para.runs:
            first_footer_run = first_footer_para.runs[0]
            first_footer_run.font.size = Pt(10)
            first_footer_run.font.name = '宋体'

        # 其他页面页眉
        header = section.header
        header_para = header.paragraphs[0]
        header_para.text = header_text
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if header_para.runs:
            header_run = header_para.runs[0]
            header_run.font.size = Pt(10)
            header_run.font.name = '宋体'

        # 其他页面页脚（包含页码）
        footer = section.footer
        footer_para = footer.paragraphs[0]
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 简化页码处理
        footer_para.text = footer_text.replace('{page}', '1')  # 简化处理，实际页码会自动更新
        if footer_para.runs:
            footer_run = footer_para.runs[0]
            footer_run.font.size = Pt(10)
            footer_run.font.name = '宋体'

    def _add_table_of_contents(self):
        """添加目录"""
        # 添加目录标题
        toc_title = self.document.add_paragraph()
        toc_title_run = toc_title.add_run("目录")
        toc_title_run.font.size = Pt(16)
        toc_title_run.font.bold = True
        toc_title_run.font.name = '宋体'
        toc_title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        self.document.add_paragraph()

        # 生成目录条目
        for level, title_text in self.toc_entries:
            toc_entry = self.document.add_paragraph()

            # 根据级别设置缩进
            indent = (level - 1) * 0.5
            toc_entry.paragraph_format.left_indent = Inches(indent)

            # 添加标题文本
            toc_run = toc_entry.add_run(title_text)
            toc_run.font.size = Pt(12)
            toc_run.font.name = '宋体'

            # 设置行间距
            toc_entry.paragraph_format.line_spacing = 1.2

    def _process_content(self, content, title_font_size, heading_font_size, body_font_size,
                        line_spacing, paragraph_spacing):
        """处理文档内容，支持表格和复杂元素"""
        lines = content.split('\n')
        i = 0

        while i < len(lines):
            line = lines[i].strip()

            if not line:
                # 添加空行
                self.document.add_paragraph()
                i += 1
                continue

            # 检测表格开始
            if line == "<!-- 表格开始 -->":
                i = self._process_table_content(lines, i, body_font_size)
                continue

            # 检测图片
            if line.startswith('[图片:') and line.endswith(']'):
                self._process_image_content(line)
                i += 1
                continue

            # 检测公式
            if line.startswith('[公式:') and line.endswith(']'):
                self._process_equation_content(line, body_font_size)
                i += 1
                continue

            # 检测绘图元素
            if line.startswith('[绘图元素:') and line.endswith(']'):
                self._process_drawing_content(line, body_font_size)
                i += 1
                continue

            # 检测标题级别
            heading_match = re.match(r'^(#{1,6})\s+(.+)$', line)
            if heading_match:
                level = len(heading_match.group(1))
                title_text = heading_match.group(2)

                # 添加标题
                heading_para = self.document.add_paragraph()
                heading_run = heading_para.add_run(title_text)

                # 根据级别设置字体大小
                if level == 1:
                    heading_run.font.size = Pt(heading_font_size + 4)
                elif level == 2:
                    heading_run.font.size = Pt(heading_font_size + 2)
                elif level == 3:
                    heading_run.font.size = Pt(heading_font_size)
                else:
                    heading_run.font.size = Pt(heading_font_size - 1)

                heading_run.font.bold = True
                heading_run.font.name = '宋体'

                # 设置段落格式
                heading_para.paragraph_format.space_before = Pt(paragraph_spacing * 2)
                heading_para.paragraph_format.space_after = Pt(paragraph_spacing)
                heading_para.paragraph_format.line_spacing = line_spacing

                # 记录标题级别
                self.heading_levels.append((level, title_text))

            else:
                # 普通段落
                para = self.document.add_paragraph()
                para_run = para.add_run(line)
                para_run.font.size = Pt(body_font_size)
                para_run.font.name = '宋体'

                # 设置行间距和段落间距
                para.paragraph_format.line_spacing = line_spacing
                para.paragraph_format.space_after = Pt(paragraph_spacing)

                # 设置首行缩进
                para.paragraph_format.first_line_indent = Inches(0.5)

            i += 1

    def _process_table_content(self, lines, start_index, body_font_size):
        """处理表格内容"""
        i = start_index + 1  # 跳过 "<!-- 表格开始 -->"
        table_lines = []

        # 收集表格行直到遇到结束标记
        while i < len(lines):
            line = lines[i].strip()
            if line == "<!-- 表格结束 -->":
                break
            if line.startswith('|') and line.endswith('|'):
                table_lines.append(line)
            i += 1

        if len(table_lines) >= 2:  # 至少需要表头和分隔行
            # 解析表格数据
            header_line = table_lines[0]
            data_lines = table_lines[2:]  # 跳过分隔行

            # 解析表头
            headers = [cell.strip() for cell in header_line.split('|')[1:-1]]

            # 创建Word表格
            if data_lines:
                table = self.document.add_table(rows=1, cols=len(headers))
                table.style = 'Table Grid'

                # 设置表头
                header_row = table.rows[0]
                for j, header in enumerate(headers):
                    cell = header_row.cells[j]
                    cell.text = header.replace('\\|', '|')
                    # 设置表头样式
                    for paragraph in cell.paragraphs:
                        for run in paragraph.runs:
                            run.font.bold = True
                            run.font.size = Pt(body_font_size)
                            run.font.name = '宋体'

                # 添加数据行
                for data_line in data_lines:
                    cells_data = [cell.strip().replace('\\|', '|') for cell in data_line.split('|')[1:-1]]
                    if len(cells_data) == len(headers):
                        row = table.add_row()
                        for j, cell_data in enumerate(cells_data):
                            cell = row.cells[j]
                            cell.text = cell_data
                            # 设置数据单元格样式
                            for paragraph in cell.paragraphs:
                                for run in paragraph.runs:
                                    run.font.size = Pt(body_font_size)
                                    run.font.name = '宋体'

        return i + 1  # 返回下一行的索引

    def _process_image_content(self, line):
        """处理图片内容"""
        # 提取图片信息
        image_info = line[4:-1]  # 去掉 "[图片:" 和 "]"

        # 添加图片占位符段落
        para = self.document.add_paragraph()
        run = para.add_run(f"[图片占位符: {image_info}]")
        run.font.size = Pt(10)
        run.font.name = '宋体'
        run.font.color.rgb = RGBColor(128, 128, 128)  # 灰色
        para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 在实际应用中，这里可以插入真实的图片
        # 例如：para.add_run().add_picture(image_path)

    def _process_equation_content(self, line, body_font_size):
        """处理公式内容"""
        # 提取公式信息
        equation_info = line[4:-1]  # 去掉 "[公式:" 和 "]"

        # 添加公式段落
        para = self.document.add_paragraph()
        run = para.add_run(f"公式: {equation_info}")
        run.font.size = Pt(body_font_size)
        run.font.name = '宋体'
        run.font.italic = True
        para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 在实际应用中，这里可以插入真实的数学公式
        # 需要使用专门的公式处理库

    def _process_drawing_content(self, line, body_font_size):
        """处理绘图元素内容"""
        # 提取绘图元素信息
        drawing_info = line[7:-1]  # 去掉 "[绘图元素:" 和 "]"

        # 添加绘图元素占位符段落
        para = self.document.add_paragraph()
        run = para.add_run(f"[绘图元素: {drawing_info}]")
        run.font.size = Pt(body_font_size)
        run.font.name = '宋体'
        run.font.color.rgb = RGBColor(0, 100, 200)  # 蓝色
        para.alignment = WD_ALIGN_PARAGRAPH.CENTER

    def get_heading_levels(self):
        """获取标题级别列表"""
        return self.heading_levels

    def extract_content_from_docx(self, docx_path):
        """从现有docx文件提取内容，包括文本、表格、图片等复杂元素"""
        try:
            doc = Document(docx_path)
            content_elements = []

            # 创建媒体文件存储目录
            media_dir = os.path.join(os.path.dirname(docx_path), 'extracted_media')
            os.makedirs(media_dir, exist_ok=True)

            # 提取文档中的所有元素（段落和表格）
            document_elements = []

            # 获取文档主体的所有子元素
            for element in doc.element.body:
                if element.tag.endswith('p'):  # 段落
                    # 找到对应的段落对象
                    for para in doc.paragraphs:
                        if para._element == element:
                            document_elements.append(('paragraph', para))
                            break
                elif element.tag.endswith('tbl'):  # 表格
                    # 找到对应的表格对象
                    for table in doc.tables:
                        if table._element == element:
                            document_elements.append(('table', table))
                            break

            # 处理每个元素
            for element_type, element in document_elements:
                if element_type == 'paragraph':
                    para_content = self._extract_paragraph_content(element, media_dir)
                    if para_content:
                        content_elements.append(para_content)
                elif element_type == 'table':
                    table_content = self._extract_table_content(element)
                    if table_content:
                        content_elements.append(table_content)

            return '\n\n'.join(content_elements)

        except Exception as e:
            print(f"提取文档内容时出错: {str(e)}")
            return ""

    def _extract_paragraph_content(self, para, media_dir):
        """提取段落内容，包括文本、图片等"""
        text = para.text.strip()

        # 检查段落中的图片和其他媒体元素
        media_elements = []
        for run in para.runs:
            # 检查图片
            media_elements.extend(self._extract_images_from_run(run, media_dir))

            # 检查公式
            media_elements.extend(self._extract_equations_from_run(run))

            # 检查其他绘图元素
            media_elements.extend(self._extract_drawings_from_run(run))

        # 如果段落为空且没有媒体元素，跳过
        if not text and not media_elements:
            return ""

        # 检查是否为标题样式
        style_name = para.style.name.lower()
        if 'heading' in style_name:
            # 提取标题级别
            if 'heading 1' in style_name:
                return f'# {text}'
            elif 'heading 2' in style_name:
                return f'## {text}'
            elif 'heading 3' in style_name:
                return f'### {text}'
            elif 'heading 4' in style_name:
                return f'#### {text}'
            elif 'heading 5' in style_name:
                return f'##### {text}'
            elif 'heading 6' in style_name:
                return f'###### {text}'
            else:
                return f'## {text}'  # 默认二级标题
        else:
            # 普通段落，包含媒体元素信息
            result = text
            if media_elements:
                result += '\n' + '\n'.join(media_elements)
            return result

    def _extract_images_from_run(self, run, media_dir):
        """从run中提取图片"""
        images = []
        try:
            # 检查run中的图片
            for drawing in run._element.xpath('.//pic:pic', namespaces={'pic': 'http://schemas.openxmlformats.org/drawingml/2006/picture'}):
                # 提取图片信息
                blip_elements = drawing.xpath('.//a:blip', namespaces={'a': 'http://schemas.openxmlformats.org/drawingml/2006/main'})
                for blip in blip_elements:
                    embed_id = blip.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
                    if embed_id:
                        # 尝试提取图片文件
                        image_info = self._save_image_from_embed_id(embed_id, media_dir, run)
                        if image_info:
                            images.append(f'[图片: {image_info}]')
                        else:
                            images.append(f'[图片: {embed_id}]')
        except Exception as e:
            # 如果xpath失败，使用简化方法
            try:
                if hasattr(run._element, 'xpath'):
                    drawings = run._element.xpath('.//a:blip')
                    for drawing in drawings:
                        embed_id = drawing.get('{http://schemas.openxmlformats.org/officeDocument/2006/relationships}embed')
                        if embed_id:
                            images.append(f'[图片: {embed_id}]')
            except:
                pass

        return images

    def _extract_equations_from_run(self, run):
        """从run中提取数学公式"""
        equations = []
        try:
            # 检查数学公式
            math_elements = run._element.xpath('.//m:oMath', namespaces={'m': 'http://schemas.openxmlformats.org/officeDocument/2006/math'})
            for math_elem in math_elements:
                # 提取公式文本（简化处理）
                math_text = ''.join(math_elem.itertext())
                if math_text.strip():
                    equations.append(f'[公式: {math_text.strip()}]')
        except:
            pass

        return equations

    def _extract_drawings_from_run(self, run):
        """从run中提取绘图元素"""
        drawings = []
        try:
            # 检查其他绘图元素（如图表、形状等）
            drawing_elements = run._element.xpath('.//wp:docPr', namespaces={'wp': 'http://schemas.openxmlformats.org/drawingml/2006/wordprocessingDrawing'})
            for drawing in drawing_elements:
                name = drawing.get('name', '')
                if name and name not in ['', 'Picture']:
                    drawings.append(f'[绘图元素: {name}]')
        except:
            pass

        return drawings

    def _save_image_from_embed_id(self, embed_id, media_dir, run):
        """根据embed_id保存图片文件"""
        try:
            # 这里需要访问文档的关系来获取实际的图片数据
            # 由于python-docx的限制，这里只返回embed_id作为占位符
            # 在实际应用中，可以通过文档的part关系来获取图片数据
            return f"image_{embed_id}"
        except:
            return None

    def _extract_table_content(self, table):
        """提取表格内容并转换为Markdown格式"""
        if not table.rows:
            return ""

        table_lines = []
        table_lines.append("<!-- 表格开始 -->")

        # 处理表头
        if len(table.rows) > 0:
            header_cells = []
            for cell in table.rows[0].cells:
                cell_text = cell.text.strip().replace('\n', ' ').replace('|', '\\|')
                header_cells.append(cell_text)

            table_lines.append('| ' + ' | '.join(header_cells) + ' |')
            table_lines.append('| ' + ' | '.join(['---'] * len(header_cells)) + ' |')

        # 处理数据行
        for row in table.rows[1:]:
            row_cells = []
            for cell in row.cells:
                cell_text = cell.text.strip().replace('\n', ' ').replace('|', '\\|')
                row_cells.append(cell_text)
            table_lines.append('| ' + ' | '.join(row_cells) + ' |')

        table_lines.append("<!-- 表格结束 -->")
        return '\n'.join(table_lines)