from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
import re
import os
from datetime import datetime

class WordDocumentGenerator:
    def __init__(self):
        self.document = None
        self.heading_levels = []
        
    def generate_document(self, content, title="文档标题", author="作者", company="公司名称",
                         header_text="文档页眉", footer_text="第 {page} 页",
                         first_page_header="首页页眉", first_page_footer="首页页脚",
                         title_font_size=16, heading_font_size=14, body_font_size=12,
                         line_spacing=1.5, paragraph_spacing=12,
                         first_page_title="文档标题", first_page_subtitle="副标题",
                         first_page_author="作者", first_page_date=None):
        
        # 创建新文档
        self.document = Document()
        
        # 设置页面边距
        sections = self.document.sections
        for section in sections:
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            section.left_margin = Inches(1.25)
            section.right_margin = Inches(1.25)
        
        # 创建首页
        self._create_first_page(first_page_title, first_page_subtitle, 
                              first_page_author, first_page_date or datetime.now().strftime('%Y年%m月%d日'), company)
        
        # 添加分页符
        self.document.add_page_break()
        
        # 设置页眉页脚
        self._setup_headers_footers(header_text, footer_text, first_page_header, first_page_footer)
        
        # 添加目录
        self._add_table_of_contents()
        
        # 添加分页符
        self.document.add_page_break()
        
        # 处理文档内容
        self._process_content(content, title_font_size, heading_font_size, body_font_size, 
                            line_spacing, paragraph_spacing)
        
        # 保存文档
        output_path = os.path.join('outputs', f'{title}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.docx')
        os.makedirs('outputs', exist_ok=True)
        self.document.save(output_path)
        
        return output_path
    
    def _create_first_page(self, title, subtitle, author, date, company):
        """创建首页"""
        # 添加标题
        title_para = self.document.add_paragraph()
        title_run = title_para.add_run(title)
        title_run.font.size = Pt(24)
        title_run.font.bold = True
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        self.document.add_paragraph()
        
        # 添加副标题
        subtitle_para = self.document.add_paragraph()
        subtitle_run = subtitle_para.add_run(subtitle)
        subtitle_run.font.size = Pt(16)
        subtitle_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加多个空行
        for _ in range(8):
            self.document.add_paragraph()
        
        # 添加作者信息
        author_para = self.document.add_paragraph()
        author_run = author_para.add_run(f"作者：{author}")
        author_run.font.size = Pt(14)
        author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加日期
        date_para = self.document.add_paragraph()
        date_run = date_para.add_run(f"日期：{date}")
        date_run.font.size = Pt(14)
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加公司信息
        if company:
            company_para = self.document.add_paragraph()
            company_run = company_para.add_run(f"公司：{company}")
            company_run.font.size = Pt(14)
            company_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    def _setup_headers_footers(self, header_text, footer_text, first_page_header, first_page_footer):
        """设置页眉页脚"""
        section = self.document.sections[0]
        
        # 设置首页不同
        section.different_first_page_header_footer = True
        
        # 首页页眉
        header = section.first_page_header
        header_para = header.paragraphs[0]
        header_para.text = first_page_header
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 首页页脚
        footer = section.first_page_footer
        footer_para = footer.paragraphs[0]
        footer_para.text = first_page_footer
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 其他页面页眉
        header = section.header
        header_para = header.paragraphs[0]
        header_para.text = header_text
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 其他页面页脚
        footer = section.footer
        footer_para = footer.paragraphs[0]
        footer_para.text = footer_text
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    def _add_table_of_contents(self):
        """添加目录"""
        # 添加目录标题
        toc_title = self.document.add_paragraph()
        toc_title_run = toc_title.add_run("目录")
        toc_title_run.font.size = Pt(16)
        toc_title_run.font.bold = True
        toc_title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        self.document.add_paragraph()
    
    def _process_content(self, content, title_font_size, heading_font_size, body_font_size, 
                        line_spacing, paragraph_spacing):
        """处理文档内容"""
        lines = content.split('\n')
        
        for line in lines:
            line = line.strip()
            if not line:
                # 添加空行
                self.document.add_paragraph()
                continue
            
            # 检测标题级别
            heading_match = re.match(r'^(#{1,6})\s+(.+)$', line)
            if heading_match:
                level = len(heading_match.group(1))
                title_text = heading_match.group(2)
                
                # 添加标题
                heading_para = self.document.add_paragraph()
                heading_run = heading_para.add_run(title_text)
                heading_run.font.size = Pt(heading_font_size)
                heading_run.font.bold = True
                
                # 设置标题样式
                if level == 1:
                    heading_para.style = self.document.styles['Heading 1']
                elif level == 2:
                    heading_para.style = self.document.styles['Heading 2']
                elif level == 3:
                    heading_para.style = self.document.styles['Heading 3']
                elif level == 4:
                    heading_para.style = self.document.styles['Heading 4']
                elif level == 5:
                    heading_para.style = self.document.styles['Heading 5']
                elif level == 6:
                    heading_para.style = self.document.styles['Heading 6']
                
                # 记录标题级别用于目录
                self.heading_levels.append((level, title_text))
                
                # 添加空行
                self.document.add_paragraph()
                
            else:
                # 普通段落
                para = self.document.add_paragraph()
                para_run = para.add_run(line)
                para_run.font.size = Pt(body_font_size)
                
                # 设置行间距
                para.paragraph_format.line_spacing = line_spacing
                para.paragraph_format.space_after = Pt(paragraph_spacing)
    
    def get_heading_levels(self):
        """获取标题级别列表"""
        return self.heading_levels 