#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Word文档生成器核心类
支持自动生成目录、页眉页脚、首页布局、自动排版等功能
"""

from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH, WD_LINE_SPACING
from docx.enum.section import WD_SECTION, WD_ORIENT
from docx.oxml.shared import OxmlElement, qn
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml
import re
import os
from datetime import datetime

class WordDocumentGenerator:
    """Word文档生成器"""

    def __init__(self):
        self.document = None
        self.heading_levels = []
        self.toc_entries = []

    def generate_document(self, content, title="文档标题", author="作者", company="公司名称",
                         header_text="文档页眉", footer_text="第 {page} 页",
                         first_page_header="首页页眉", first_page_footer="首页页脚",
                         title_font_size=16, heading_font_size=14, body_font_size=12,
                         line_spacing=1.5, paragraph_spacing=12,
                         first_page_title="文档标题", first_page_subtitle="副标题",
                         first_page_author="作者", first_page_date=None,
                         output_dir="outputs"):
        """
        生成Word文档

        Args:
            content: 文档内容（支持Markdown格式标题）
            title: 文档标题
            author: 作者
            company: 公司名称
            header_text: 页眉文本
            footer_text: 页脚文本（支持{page}占位符）
            first_page_header: 首页页眉
            first_page_footer: 首页页脚
            title_font_size: 标题字体大小
            heading_font_size: 标题字体大小
            body_font_size: 正文字体大小
            line_spacing: 行间距
            paragraph_spacing: 段落间距
            first_page_title: 首页标题
            first_page_subtitle: 首页副标题
            first_page_author: 首页作者
            first_page_date: 首页日期
            output_dir: 输出目录

        Returns:
            str: 生成的文档路径
        """

        # 创建新文档
        self.document = Document()
        self.heading_levels = []
        self.toc_entries = []

        # 设置页面属性
        self._setup_page_settings()

        # 预处理内容，提取标题信息
        self._extract_headings(content)

        # 创建首页
        self._create_first_page(first_page_title, first_page_subtitle,
                              first_page_author, first_page_date or datetime.now().strftime('%Y年%m月%d日'),
                              company, title_font_size)

        # 添加分页符
        self.document.add_page_break()

        # 设置页眉页脚
        self._setup_headers_footers(header_text, footer_text, first_page_header, first_page_footer)

        # 添加目录
        self._add_table_of_contents()

        # 添加分页符
        self.document.add_page_break()

        # 处理文档内容
        self._process_content(content, title_font_size, heading_font_size, body_font_size,
                            line_spacing, paragraph_spacing)

        # 保存文档
        os.makedirs(output_dir, exist_ok=True)
        output_path = os.path.join(output_dir, f'{title}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.docx')
        self.document.save(output_path)

        return output_path

    def _setup_page_settings(self):
        """设置页面属性"""
        sections = self.document.sections
        for section in sections:
            # 设置页边距
            section.top_margin = Inches(1)
            section.bottom_margin = Inches(1)
            section.left_margin = Inches(1.25)
            section.right_margin = Inches(1.25)

            # 设置页面方向和大小
            section.orientation = WD_ORIENT.PORTRAIT
            section.page_width = Inches(8.5)
            section.page_height = Inches(11)

    def _extract_headings(self, content):
        """预处理内容，提取标题信息用于生成目录"""
        lines = content.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 检测标题级别
            heading_match = re.match(r'^(#{1,6})\s+(.+)$', line)
            if heading_match:
                level = len(heading_match.group(1))
                title_text = heading_match.group(2)
                self.toc_entries.append((level, title_text))

    def _create_first_page(self, title, subtitle, author, date, company, title_font_size):
        """创建首页"""
        # 添加主标题
        title_para = self.document.add_paragraph()
        title_run = title_para.add_run(title)
        title_run.font.size = Pt(title_font_size + 8)  # 首页标题更大
        title_run.font.bold = True
        title_run.font.name = '宋体'
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()

        # 添加副标题
        if subtitle and subtitle != title:
            subtitle_para = self.document.add_paragraph()
            subtitle_run = subtitle_para.add_run(subtitle)
            subtitle_run.font.size = Pt(title_font_size + 2)
            subtitle_run.font.name = '宋体'
            subtitle_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 添加空行
            for _ in range(2):
                self.document.add_paragraph()

        # 添加更多空行使内容居中
        for _ in range(8):
            self.document.add_paragraph()

        # 添加作者信息
        author_para = self.document.add_paragraph()
        author_run = author_para.add_run(f"作者：{author}")
        author_run.font.size = Pt(14)
        author_run.font.name = '宋体'
        author_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        self.document.add_paragraph()

        # 添加公司信息
        company_para = self.document.add_paragraph()
        company_run = company_para.add_run(company)
        company_run.font.size = Pt(14)
        company_run.font.name = '宋体'
        company_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()

        # 添加日期
        date_para = self.document.add_paragraph()
        date_run = date_para.add_run(date)
        date_run.font.size = Pt(14)
        date_run.font.name = '宋体'
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

    def _setup_headers_footers(self, header_text, footer_text, first_page_header, first_page_footer):
        """设置页眉页脚"""
        section = self.document.sections[0]

        # 设置不同的首页页眉页脚
        section.different_first_page_header_footer = True

        # 首页页眉
        first_header = section.first_page_header
        first_header_para = first_header.paragraphs[0]
        first_header_para.text = first_page_header
        first_header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if first_header_para.runs:
            first_header_run = first_header_para.runs[0]
            first_header_run.font.size = Pt(10)
            first_header_run.font.name = '宋体'

        # 首页页脚
        first_footer = section.first_page_footer
        first_footer_para = first_footer.paragraphs[0]
        first_footer_para.text = first_page_footer
        first_footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if first_footer_para.runs:
            first_footer_run = first_footer_para.runs[0]
            first_footer_run.font.size = Pt(10)
            first_footer_run.font.name = '宋体'

        # 其他页面页眉
        header = section.header
        header_para = header.paragraphs[0]
        header_para.text = header_text
        header_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        if header_para.runs:
            header_run = header_para.runs[0]
            header_run.font.size = Pt(10)
            header_run.font.name = '宋体'

        # 其他页面页脚（包含页码）
        footer = section.footer
        footer_para = footer.paragraphs[0]
        footer_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 简化页码处理
        footer_para.text = footer_text.replace('{page}', '1')  # 简化处理，实际页码会自动更新
        if footer_para.runs:
            footer_run = footer_para.runs[0]
            footer_run.font.size = Pt(10)
            footer_run.font.name = '宋体'

    def _add_table_of_contents(self):
        """添加目录"""
        # 添加目录标题
        toc_title = self.document.add_paragraph()
        toc_title_run = toc_title.add_run("目录")
        toc_title_run.font.size = Pt(16)
        toc_title_run.font.bold = True
        toc_title_run.font.name = '宋体'
        toc_title.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        self.document.add_paragraph()

        # 生成目录条目
        for level, title_text in self.toc_entries:
            toc_entry = self.document.add_paragraph()

            # 根据级别设置缩进
            indent = (level - 1) * 0.5
            toc_entry.paragraph_format.left_indent = Inches(indent)

            # 添加标题文本
            toc_run = toc_entry.add_run(title_text)
            toc_run.font.size = Pt(12)
            toc_run.font.name = '宋体'

            # 设置行间距
            toc_entry.paragraph_format.line_spacing = 1.2

    def _process_content(self, content, title_font_size, heading_font_size, body_font_size,
                        line_spacing, paragraph_spacing):
        """处理文档内容"""
        lines = content.split('\n')

        for line in lines:
            line = line.strip()
            if not line:
                # 添加空行
                self.document.add_paragraph()
                continue

            # 检测标题级别
            heading_match = re.match(r'^(#{1,6})\s+(.+)$', line)
            if heading_match:
                level = len(heading_match.group(1))
                title_text = heading_match.group(2)

                # 添加标题
                heading_para = self.document.add_paragraph()
                heading_run = heading_para.add_run(title_text)

                # 根据级别设置字体大小
                if level == 1:
                    heading_run.font.size = Pt(heading_font_size + 4)
                elif level == 2:
                    heading_run.font.size = Pt(heading_font_size + 2)
                elif level == 3:
                    heading_run.font.size = Pt(heading_font_size)
                else:
                    heading_run.font.size = Pt(heading_font_size - 1)

                heading_run.font.bold = True
                heading_run.font.name = '宋体'

                # 设置段落格式
                heading_para.paragraph_format.space_before = Pt(paragraph_spacing * 2)
                heading_para.paragraph_format.space_after = Pt(paragraph_spacing)
                heading_para.paragraph_format.line_spacing = line_spacing

                # 记录标题级别
                self.heading_levels.append((level, title_text))

            else:
                # 普通段落
                para = self.document.add_paragraph()
                para_run = para.add_run(line)
                para_run.font.size = Pt(body_font_size)
                para_run.font.name = '宋体'

                # 设置行间距和段落间距
                para.paragraph_format.line_spacing = line_spacing
                para.paragraph_format.space_after = Pt(paragraph_spacing)

                # 设置首行缩进
                para.paragraph_format.first_line_indent = Inches(0.5)

    def get_heading_levels(self):
        """获取标题级别列表"""
        return self.heading_levels

    def extract_content_from_docx(self, docx_path):
        """从现有docx文件提取内容并转换为Markdown格式"""
        try:
            doc = Document(docx_path)
            content_lines = []

            for para in doc.paragraphs:
                text = para.text.strip()
                if not text:
                    content_lines.append('')
                    continue

                # 检查是否为标题样式
                style_name = para.style.name.lower()
                if 'heading' in style_name:
                    # 提取标题级别
                    if 'heading 1' in style_name:
                        content_lines.append(f'# {text}')
                    elif 'heading 2' in style_name:
                        content_lines.append(f'## {text}')
                    elif 'heading 3' in style_name:
                        content_lines.append(f'### {text}')
                    elif 'heading 4' in style_name:
                        content_lines.append(f'#### {text}')
                    elif 'heading 5' in style_name:
                        content_lines.append(f'##### {text}')
                    elif 'heading 6' in style_name:
                        content_lines.append(f'###### {text}')
                    else:
                        content_lines.append(f'## {text}')  # 默认二级标题
                else:
                    # 普通段落
                    content_lines.append(text)

            return '\n'.join(content_lines)

        except Exception as e:
            print(f"提取文档内容时出错: {str(e)}")
            return ""