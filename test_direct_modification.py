#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试直接修改方案
验证基于原文档直接修改的重排版功能
"""

from document_reformatter import DocumentReformatter
from docx import Document
import os

def test_direct_modification():
    """测试直接修改方案"""
    print("=== 测试直接修改重排版方案 ===")
    
    source_file = 'testdoc.docx'
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return
    
    reformatter = DocumentReformatter()
    
    # 1. 获取原文档信息
    print("1. 分析原文档...")
    original_info = reformatter.get_document_info(source_file)
    if original_info:
        print(f"   原文档段落数: {original_info['paragraphs']}")
        print(f"   原文档表格数: {original_info['tables']}")
        print(f"   原文档图片数: {original_info['images']}")
        print(f"   原文档标题数: {len(original_info['headings'])}")
        print(f"   原文档大小: {original_info['file_size']} 字节")
        
        if original_info['headings']:
            print("   发现的标题:")
            for i, heading in enumerate(original_info['headings'][:5]):  # 只显示前5个
                print(f"     {i+1}. {heading}")
    
    # 2. 执行直接修改重排版
    print("\n2. 执行直接修改重排版...")
    try:
        output_path = reformatter.reformat_document(
            source_path=source_file,
            title="testdoc直接修改重排版",
            author="智能文档处理系统",
            company="保留原始元素的重排版方案",
            header_text="直接修改重排版 | 保留所有原始元素",
            footer_text="第 {page} 页 | 智能处理",
            first_page_header="智能文档重排版",
            first_page_footer="基于原文档直接修改",
            first_page_title="testdoc直接修改重排版",
            first_page_subtitle="保留图片、表格、公式等所有原始元素\n基于原文档直接修改的智能重排版",
            first_page_author="智能文档处理系统",
            add_toc=True
        )
        
        print(f"   ✅ 重排版成功: {output_path}")
        
        # 3. 分析重排版后的文档
        print("\n3. 分析重排版后的文档...")
        new_info = reformatter.get_document_info(output_path)
        if new_info:
            print(f"   重排版文档段落数: {new_info['paragraphs']}")
            print(f"   重排版文档表格数: {new_info['tables']}")
            print(f"   重排版文档图片数: {new_info['images']}")
            print(f"   重排版文档标题数: {len(new_info['headings'])}")
            print(f"   重排版文档大小: {new_info['file_size']} 字节")
        
        # 4. 对比分析
        print("\n4. 对比分析...")
        if original_info and new_info:
            print("   元素保留情况:")
            if original_info['tables'] == new_info['tables']:
                print(f"   ✅ 表格完全保留: {original_info['tables']} → {new_info['tables']}")
            else:
                print(f"   ❌ 表格数量变化: {original_info['tables']} → {new_info['tables']}")
            
            if original_info['images'] == new_info['images']:
                print(f"   ✅ 图片完全保留: {original_info['images']} → {new_info['images']}")
            else:
                print(f"   ❌ 图片数量变化: {original_info['images']} → {new_info['images']}")
            
            print(f"   📊 段落数变化: {original_info['paragraphs']} → {new_info['paragraphs']} (增加了首页和目录)")
            print(f"   📊 文件大小变化: {original_info['file_size']} → {new_info['file_size']} 字节")
        
        return output_path
        
    except Exception as e:
        print(f"   ❌ 重排版失败: {str(e)}")
        return None

def test_detailed_document_analysis(doc_path):
    """详细分析生成的文档"""
    if not doc_path or not os.path.exists(doc_path):
        print("❌ 文档不存在，跳过详细分析")
        return
    
    print(f"\n=== 详细分析生成的文档 ===")
    
    try:
        doc = Document(doc_path)
        
        print(f"文档路径: {doc_path}")
        print(f"总段落数: {len(doc.paragraphs)}")
        print(f"总表格数: {len(doc.tables)}")
        
        # 分析前30个段落
        print("\n前30个段落内容:")
        for i, para in enumerate(doc.paragraphs[:30]):
            text = para.text.strip()
            if text:
                print(f"{i+1:2d}: [{para.style.name}] {text[:60]}...")
            elif i < 10:  # 只显示前10个空段落
                print(f"{i+1:2d}: [空段落]")
        
        # 检查目录
        print("\n目录检查:")
        toc_found = False
        toc_entries = 0
        for i, para in enumerate(doc.paragraphs):
            if '目录' in para.text and para.text.strip() == '目录':
                print(f"   ✅ 找到目录标题在第{i+1}段")
                toc_found = True
                # 检查目录条目
                for j in range(i+1, min(i+20, len(doc.paragraphs))):
                    next_para = doc.paragraphs[j]
                    if next_para.text.strip() and '目录' not in next_para.text:
                        toc_entries += 1
                        if toc_entries <= 5:  # 只显示前5个目录条目
                            print(f"     目录条目{toc_entries}: {next_para.text.strip()}")
                break
        
        if not toc_found:
            print("   ❌ 未找到目录")
        else:
            print(f"   ✅ 找到目录，包含 {toc_entries} 个条目")
        
        # 检查表格
        print(f"\n表格检查:")
        for i, table in enumerate(doc.tables):
            print(f"   表格{i+1}: {len(table.rows)}行 x {len(table.columns)}列")
            if len(table.rows) > 0 and len(table.columns) > 0:
                first_cell = table.rows[0].cells[0].text.strip()[:30]
                print(f"     首个单元格: {first_cell}...")
        
        # 检查图片和其他元素
        print(f"\n媒体元素检查:")
        image_count = 0
        equation_count = 0
        
        for para in doc.paragraphs:
            for run in para.runs:
                # 检查图片
                if run._element.xpath('.//a:blip'):
                    image_count += 1
                # 检查公式
                if run._element.xpath('.//m:oMath'):
                    equation_count += 1
        
        print(f"   图片数量: {image_count}")
        print(f"   公式数量: {equation_count}")
        
    except Exception as e:
        print(f"详细分析时出错: {str(e)}")

def compare_with_original():
    """与原文档进行详细对比"""
    print(f"\n=== 与原文档详细对比 ===")
    
    original_path = 'testdoc.docx'
    if not os.path.exists(original_path):
        print("❌ 原文档不存在")
        return
    
    # 找到最新的重排版文档
    output_files = [f for f in os.listdir('outputs') if f.startswith('testdoc直接修改重排版') and f.endswith('.docx')]
    if not output_files:
        print("❌ 未找到重排版文档")
        return
    
    latest_file = max(output_files, key=lambda x: os.path.getmtime(os.path.join('outputs', x)))
    reformatted_path = os.path.join('outputs', latest_file)
    
    try:
        original_doc = Document(original_path)
        reformatted_doc = Document(reformatted_path)
        
        print("对比结果:")
        print(f"   原文档段落数: {len(original_doc.paragraphs)}")
        print(f"   重排版段落数: {len(reformatted_doc.paragraphs)}")
        print(f"   原文档表格数: {len(original_doc.tables)}")
        print(f"   重排版表格数: {len(reformatted_doc.tables)}")
        
        # 详细对比表格
        if len(original_doc.tables) == len(reformatted_doc.tables):
            print("   ✅ 表格数量一致")
            for i, (orig_table, reform_table) in enumerate(zip(original_doc.tables, reformatted_doc.tables)):
                if (len(orig_table.rows) == len(reform_table.rows) and 
                    len(orig_table.columns) == len(reform_table.columns)):
                    print(f"   ✅ 表格{i+1}结构一致: {len(orig_table.rows)}行 x {len(orig_table.columns)}列")
                else:
                    print(f"   ❌ 表格{i+1}结构不一致")
        else:
            print("   ❌ 表格数量不一致")
        
        print(f"   原文档大小: {os.path.getsize(original_path)} 字节")
        print(f"   重排版大小: {os.path.getsize(reformatted_path)} 字节")
        
    except Exception as e:
        print(f"对比时出错: {str(e)}")

if __name__ == "__main__":
    print("开始测试直接修改重排版方案...")
    
    # 1. 测试直接修改
    output_doc = test_direct_modification()
    
    # 2. 详细分析生成的文档
    test_detailed_document_analysis(output_doc)
    
    # 3. 与原文档对比
    compare_with_original()
    
    print("\n=== 测试完成 ===")
    print("请检查outputs目录中的重排版文档，验证所有元素是否完整保留。")
