#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
毕业论文格式化器 V4 改进版 - 更好地保持文档完整性
重点解决文本框等特殊元素的保持问题
"""

import os
import shutil
from datetime import datetime
from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.oxml import parse_xml
from docx.oxml.ns import nsdecls, qn
import re

class ThesisFormatterV4Improved:
    """毕业论文格式化器 V4 改进版"""
    
    def __init__(self):
        self.document = None
        self.font_sizes = {}
        
    def format_thesis_document(self, source_path, **kwargs):
        """格式化毕业论文文档 - V4改进版"""
        
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"源文档不存在: {source_path}")

        # 提取参数
        student_info = kwargs.get('student_info', {})
        student_name = student_info.get('name', '')
        student_id = student_info.get('student_id', '')
        major = student_info.get('major', '')
        class_name = student_info.get('class_name', '')
        advisor = student_info.get('advisor', '')
        college = student_info.get('college', '')
        
        output_name = kwargs.get('output_name', '格式化论文')
        
        # 字体大小设置
        self.font_sizes = {
            'cover_main_title': kwargs.get('cover_main_title_size', 42),
            'cover_thesis_title': kwargs.get('cover_thesis_title_size', 22),
            'cover_info': kwargs.get('cover_info_size', 14),
            'chapter_title': kwargs.get('chapter_title_size', 18),
            'section_title': kwargs.get('section_title_size', 16),
            'body_text': kwargs.get('body_text_size', 12),
            'toc_content': kwargs.get('toc_content_size', 12),
        }
        
        # 创建输出目录
        output_dir = 'outputs'
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"毕业论文_{student_name or '学生'}_{timestamp}.docx"
        output_path = os.path.join(output_dir, output_filename)
        
        try:
            print("🔄 使用V4改进版处理文档...")
            
            # 创建新文档
            self.document = Document()
            
            # 1. 设置页面属性
            self._setup_page_settings()
            
            # 2. 创建封面页
            self._create_cover_page(output_name, student_name, student_id, major, 
                                  class_name, advisor, college)
            
            # 3. 分页符 + 诚信声明页
            self.document.add_page_break()
            self._create_declaration_page()
            
            # 4. 使用改进的方法插入内容
            self.document.add_page_break()
            self._insert_content_with_special_elements(source_path)
            
            # 5. 保存文档
            self.document.save(output_path)
            
            print(f"✅ V4改进版处理完成！")
            print(f"📄 输出文件: {output_path}")
            return output_path
            
        except Exception as e:
            if os.path.exists(output_path):
                os.remove(output_path)
            raise e
    
    def _setup_page_settings(self):
        """设置页面属性"""
        section = self.document.sections[0]
        
        # 设置页边距
        section.top_margin = Inches(0.98)
        section.bottom_margin = Inches(0.79)
        section.left_margin = Inches(1.25)
        section.right_margin = Inches(1.25)
        
        # 设置页面大小（A4）
        section.page_width = Inches(8.27)
        section.page_height = Inches(11.69)
    
    def _create_cover_page(self, thesis_title, student_name, student_id, major, 
                          class_name, advisor, college):
        """创建封面页"""
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 毕业论文标题
        title_para = self.document.add_paragraph()
        title_run = title_para.add_run("毕业论文")
        title_run.font.name = '黑体'
        title_run.font.size = Pt(self.font_sizes['cover_main_title'])
        title_run.font.bold = True
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()

        # 论文题目
        thesis_title_para = self.document.add_paragraph()
        thesis_title_run = thesis_title_para.add_run(thesis_title)
        thesis_title_run.font.name = '黑体'
        thesis_title_run.font.size = Pt(self.font_sizes['cover_thesis_title'])
        thesis_title_run.font.bold = True
        thesis_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        for _ in range(4):
            self.document.add_paragraph()
        
        # 学生信息
        info_items = [
            ("学生姓名：", student_name),
            ("学    号：", student_id),
            ("专    业：", major),
            ("班    级：", class_name),
            ("指导教师：", advisor),
            ("学    院：", college),
        ]
        
        for label, value in info_items:
            info_para = self.document.add_paragraph()
            label_run = info_para.add_run(label)
            label_run.font.name = '宋体'
            label_run.font.size = Pt(self.font_sizes['cover_info'])
            
            value_run = info_para.add_run(value)
            value_run.font.name = '宋体'
            value_run.font.size = Pt(self.font_sizes['cover_info'])
            
            info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行和日期
        for _ in range(3):
            self.document.add_paragraph()
        
        date_para = self.document.add_paragraph()
        date_run = date_para.add_run("二○二五年七月")
        date_run.font.name = '宋体'
        date_run.font.size = Pt(self.font_sizes['cover_info'])
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    def _create_declaration_page(self):
        """创建诚信声明页"""
        # 标题
        title_para = self.document.add_paragraph()
        title_run = title_para.add_run("毕业论文诚信声明")
        title_run.font.name = '黑体'
        title_run.font.size = Pt(18)
        title_run.font.bold = True
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 空行
        self.document.add_paragraph()
        
        # 声明内容
        decl_text = "所呈交的毕业论文是本人在指导老师的指导下，独立研究、写作的成果。毕业设计(论文)中所引用是他人的无论以何种方式发布的文字、研究成果，均在毕业设计(论文)中以明确方式标明。"
        
        decl_para = self.document.add_paragraph()
        decl_run = decl_para.add_run(decl_text)
        decl_run.font.name = '宋体'
        decl_run.font.size = Pt(12)
        decl_para.paragraph_format.first_line_indent = Inches(0.5)
        decl_para.paragraph_format.line_spacing = 1.5
        
        # 空行
        for _ in range(3):
            self.document.add_paragraph()
        
        # 签名行
        signature_para = self.document.add_paragraph()
        signature_run = signature_para.add_run("学生签名：________________    日期：________________")
        signature_run.font.name = '宋体'
        signature_run.font.size = Pt(12)
        signature_para.alignment = WD_ALIGN_PARAGRAPH.RIGHT

    def _insert_content_with_special_elements(self, source_path):
        """改进的内容插入方法，重点保持特殊元素"""
        try:
            source_doc = Document(source_path)

            print(f"📄 开始插入原文档内容，共{len(source_doc.paragraphs)}段")

            # 找到摘要结束位置
            abstract_end_position = self._find_abstract_end_position(source_doc)
            print(f"📍 摘要结束位置：第{abstract_end_position + 1}段")

            # 插入摘要部分（从开始到摘要结束）
            self._insert_content_range_improved(source_doc, 0, abstract_end_position, "摘要部分")

            # 在摘要后插入目录
            self.document.add_page_break()
            self._create_table_of_contents(source_path)

            # 插入正文部分（从摘要结束后到文档结束）
            self.document.add_page_break()
            self._insert_content_range_improved(source_doc, abstract_end_position + 1,
                                              len(source_doc.paragraphs) - 1, "正文部分")

        except Exception as e:
            print(f"❌ 插入内容时出错: {e}")
            import traceback
            traceback.print_exc()

    def _insert_content_range_improved(self, source_doc, start_index, end_index, section_name):
        """改进的内容范围插入，重点保持特殊元素"""
        print(f"📄 插入{section_name}：第{start_index+1}段到第{end_index+1}段")

        for i in range(start_index, min(end_index + 1, len(source_doc.paragraphs))):
            para = source_doc.paragraphs[i]
            text = para.text.strip()

            # 跳过"正文"、"目录"等标识
            if text in ['正文', '目录', '目    录']:
                continue

            # 检查段落是否包含特殊元素
            has_special_elements = self._has_special_elements(para)

            if has_special_elements:
                print(f"🔍 第{i+1}段包含特殊元素，使用XML复制方法")
                self._copy_paragraph_with_xml(para)
            else:
                # 普通段落使用改进的复制方法
                self._copy_paragraph_improved(para)

    def _has_special_elements(self, para):
        """检查段落是否包含特殊元素（文本框、绘图等）"""
        try:
            if para._element is not None:
                xml_str = str(para._element.xml)
                return any(keyword in xml_str.lower() for keyword in
                          ['textbox', 'drawing', 'shape', 'pic:', 'w:drawing'])
        except:
            pass
        return False

    def _copy_paragraph_with_xml(self, source_para):
        """使用XML方法复制包含特殊元素的段落"""
        try:
            # 创建新段落
            new_para = self.document.add_paragraph()

            # 尝试直接复制XML元素
            source_xml = source_para._element

            # 复制段落的所有子元素
            for child in source_xml:
                try:
                    # 创建新的XML元素并添加到新段落
                    new_element = parse_xml(child.xml)
                    new_para._element.append(new_element)
                except Exception as e:
                    print(f"⚠️ XML元素复制失败: {e}")
                    # 如果XML复制失败，回退到基本方法
                    self._copy_paragraph_basic(source_para, new_para)
                    break

        except Exception as e:
            print(f"❌ XML段落复制失败: {e}")
            # 如果完全失败，使用基本方法
            new_para = self.document.add_paragraph()
            self._copy_paragraph_basic(source_para, new_para)

    def _copy_paragraph_improved(self, source_para):
        """改进的普通段落复制方法"""
        new_para = self.document.add_paragraph()

        # 复制所有runs
        for run in source_para.runs:
            new_run = new_para.add_run(run.text)

            # 复制字体格式
            if run.font.size:
                new_run.font.size = run.font.size
            if run.font.name:
                new_run.font.name = run.font.name
            if run.font.bold is not None:
                new_run.font.bold = run.font.bold
            if run.font.italic is not None:
                new_run.font.italic = run.font.italic
            if run.font.underline is not None:
                new_run.font.underline = run.font.underline

        # 复制段落格式
        if source_para.alignment is not None:
            new_para.alignment = source_para.alignment
        if source_para.paragraph_format.first_line_indent:
            new_para.paragraph_format.first_line_indent = source_para.paragraph_format.first_line_indent
        if source_para.paragraph_format.line_spacing:
            new_para.paragraph_format.line_spacing = source_para.paragraph_format.line_spacing

    def _copy_paragraph_basic(self, source_para, target_para):
        """基本的段落复制方法（备用）"""
        for run in source_para.runs:
            new_run = target_para.add_run(run.text)

            # 复制基本格式
            if run.font.size:
                new_run.font.size = run.font.size
            if run.font.name:
                new_run.font.name = run.font.name
            if run.font.bold is not None:
                new_run.font.bold = run.font.bold

    def _find_abstract_end_position(self, source_doc):
        """找到摘要结束位置（在真正的正文开始之前）"""
        abstract_start = -1
        first_chapter_start = -1

        for i, para in enumerate(source_doc.paragraphs):
            text = para.text.strip()
            if text:
                # 查找摘要开始 - 排除目录条目
                if (text == '摘要' or text == '摘    要') and '…' not in text and abstract_start == -1:
                    abstract_start = i
                    print(f"✅ 找到摘要开始：第{i+1}段")

                # 查找真正的正文开始标志
                elif abstract_start != -1 and first_chapter_start == -1:
                    # 查找"引言"、"引    言"
                    if text == '引言' or text == '引    言':
                        first_chapter_start = i
                        print(f"✅ 找到正文开始（引言）：第{i+1}段")
                        break
                    # 查找"第X章"格式的章标题
                    elif re.match(r'^第[一二三四五六七八九十\d]+章', text) and '…' not in text:
                        first_chapter_start = i
                        print(f"✅ 找到正文开始（第一章）：第{i+1}段 - {text}")
                        break
                    # 查找数字章节标题（如"1 引言"、"1. 引言"等）
                    elif re.match(r'^\d+[\s\.]+[引绪]', text) and '…' not in text:
                        first_chapter_start = i
                        print(f"✅ 找到正文开始（数字章节）：第{i+1}段 - {text}")
                        break
                    # 查找"正文"标识后的第一个章节
                    elif text == '正文':
                        # 继续向后查找真正的章节开始
                        for j in range(i + 1, min(i + 10, len(source_doc.paragraphs))):
                            next_text = source_doc.paragraphs[j].text.strip()
                            if next_text and (
                                re.match(r'^\d+[\s\.]+', next_text) or  # 数字开头的章节
                                next_text in ['引言', '引    言', '绪论'] or
                                re.match(r'^第[一二三四五六七八九十\d]+章', next_text)
                            ):
                                first_chapter_start = j
                                print(f"✅ 找到正文开始（正文标识后）：第{j+1}段 - {next_text}")
                                break
                        if first_chapter_start != -1:
                            break

        # 如果找到了正文开始位置，摘要结束就在正文开始前一段
        if first_chapter_start != -1:
            abstract_end = first_chapter_start - 1
            print(f"✅ 确定摘要结束位置：第{abstract_end+1}段（在正文开始之前）")
            return abstract_end

        # 如果没有找到明确的正文开始，使用智能判断
        if abstract_start != -1:
            abstract_end = min(abstract_start + 60, len(source_doc.paragraphs) - 1)
            print(f"✅ 使用默认摘要范围：第{abstract_start+1}段到第{abstract_end+1}段")
            return abstract_end

        # 如果完全没找到摘要，返回一个合理的默认位置
        print("⚠️ 未找到摘要开始，使用默认位置")
        return min(80, len(source_doc.paragraphs) // 2)

    def _create_table_of_contents(self, source_path):
        """创建目录页"""
        try:
            source_doc = Document(source_path)

            # 目录标题
            toc_title_para = self.document.add_paragraph()
            toc_title_run = toc_title_para.add_run("目    录")
            toc_title_run.font.name = '黑体'
            toc_title_run.font.size = Pt(18)
            toc_title_run.font.bold = True
            toc_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            # 空行
            self.document.add_paragraph()

            # 提取目录条目
            toc_entries = []

            # 添加固定的摘要条目
            toc_entries.append(("中文摘要", "Ⅰ"))
            toc_entries.append(("英文摘要", "Ⅱ"))

            # 从源文档提取章节标题
            page_num = 1
            for para in source_doc.paragraphs:
                text = para.text.strip()
                if text:
                    # 查找章节标题
                    if (re.match(r'^第[一二三四五六七八九十\d]+章', text) or
                        text in ['引言', '绪论', '结论', '致谢', '参考文献']):
                        toc_entries.append((text, str(page_num)))
                        page_num += 1
                    elif re.match(r'^\d+[\s\.]+', text) and len(text) < 50:
                        toc_entries.append((text, str(page_num)))
                        page_num += 1

            # 生成目录条目
            for title, page in toc_entries:
                toc_para = self.document.add_paragraph()

                # 标题
                title_run = toc_para.add_run(title)
                title_run.font.name = '宋体'
                title_run.font.size = Pt(self.font_sizes['toc_content'])

                # 点线
                dots_run = toc_para.add_run("…" * (50 - len(title)))
                dots_run.font.name = '宋体'
                dots_run.font.size = Pt(self.font_sizes['toc_content'])

                # 页码
                page_run = toc_para.add_run(page)
                page_run.font.name = '宋体'
                page_run.font.size = Pt(self.font_sizes['toc_content'])

                toc_para.alignment = WD_ALIGN_PARAGRAPH.LEFT

            print(f"✅ 从文档中提取了 {len(toc_entries)} 个目录条目")

        except Exception as e:
            print(f"❌ 创建目录失败: {e}")
            # 创建简单目录
            toc_para = self.document.add_paragraph()
            toc_run = toc_para.add_run("目录")
            toc_run.font.name = '黑体'
            toc_run.font.size = Pt(18)
            toc_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
