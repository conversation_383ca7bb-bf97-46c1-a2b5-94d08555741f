#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查源文档结构
"""

from docx import Document

def check_source_structure():
    """检查源文档的前20段内容"""
    
    source_doc = Document('testdoc.docx')
    
    print('=== 源文档前20段内容 ===')
    
    for i, para in enumerate(source_doc.paragraphs[:20]):
        text = para.text.strip()
        if text:
            print(f'第{i+1}段: {text}')
        else:
            print(f'第{i+1}段: [空段落]')
    
    print(f'\n总段落数: {len(source_doc.paragraphs)}')

if __name__ == "__main__":
    check_source_structure()
