#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试字体大小配置API
"""

import requests
import os
from docx import Document

def test_font_api():
    """测试字体大小配置API"""
    
    print('=== 测试字体大小配置API ===')
    
    url = 'http://localhost:5000/format-thesis'
    
    # 准备文件和数据（包含自定义字体大小）
    files = {'file': open('testdoc.docx', 'rb')}
    data = {
        'thesis_title': '基于人工智能的文档处理系统设计与实现',
        'student_name': 'API字体测试',
        'student_id': '2021001250',
        'department': '智能科学与技术系',
        'major': '人工智能技术应用',
        'class_name': 'AI2101班',
        'supervisor': 'API教授',
        'supervisor_title': '教授',
        'enterprise_supervisor': 'API工程师',
        'school_name': '江西泰豪动漫职业学院',
        'year': '2025',
        'thesis_date': '二○二五年七月',
        
        # 自定义字体大小参数
        'cover_main_title_size': '50',  # 更大的封面标题
        'cover_thesis_title_size': '26',  # 更大的论文题目
        'cover_info_size': '15',  # 稍大的学生信息
        'section_title_size': '17',  # 稍大的部分标题
        'body_text_size': '13',  # 稍大的正文
        'chinese_abstract_size': '12',  # 中文摘要
        'english_abstract_size': '12',  # 英文摘要
        'toc_content_size': '12',  # 目录
        'table_text_size': '10',  # 表格文字
        'header_footer_size': '9',  # 页眉页脚
    }
    
    try:
        print('正在测试字体大小配置API...')
        response = requests.post(url, files=files, data=data)
        
        if response.status_code == 200:
            # 保存返回的文件
            with open('api_font_test.docx', 'wb') as f:
                f.write(response.content)
            print('✅ 字体大小配置API测试成功！')
            print(f'文件已保存: api_font_test.docx')
            print(f'文件大小: {len(response.content)} 字节')
            
            # 分析生成的文档
            doc = Document('api_font_test.docx')
            print(f'段落数: {len(doc.paragraphs)}')
            print(f'表格数: {len(doc.tables)}')
            
            # 检查字体大小
            print('\n=== API字体大小检查 ===')
            
            # 检查封面标题
            for i, para in enumerate(doc.paragraphs[:10]):
                text = para.text.strip()
                if '毕业论文' in text and len(text) <= 10:
                    if para.runs:
                        font_size = para.runs[0].font.size
                        if font_size:
                            print(f'封面主标题字体大小: {font_size.pt}pt (期望: 50pt)')
                    break
            
            # 检查论文题目
            for i, para in enumerate(doc.paragraphs[:15]):
                text = para.text.strip()
                if '基于人工智能' in text:
                    if para.runs:
                        font_size = para.runs[0].font.size
                        if font_size:
                            print(f'论文题目字体大小: {font_size.pt}pt (期望: 26pt)')
                    break
            
            # 检查学生信息表格
            for table in doc.tables:
                if len(table.rows) == 8 and len(table.columns) == 2:
                    first_cell = table.rows[0].cells[0]
                    if first_cell.paragraphs and first_cell.paragraphs[0].runs:
                        font_size = first_cell.paragraphs[0].runs[0].font.size
                        if font_size:
                            print(f'学生信息字体大小: {font_size.pt}pt (期望: 15pt)')
                    break
            
            # 检查摘要标题
            for i, para in enumerate(doc.paragraphs):
                text = para.text.strip()
                if '摘 要' in text or '摘要' in text:
                    if para.runs:
                        font_size = para.runs[0].font.size
                        if font_size:
                            print(f'摘要标题字体大小: {font_size.pt}pt (期望: 17pt)')
                    break
            
            # 检查正文内容
            for i, para in enumerate(doc.paragraphs):
                text = para.text.strip()
                if '正文内容' in text:
                    # 检查正文后面的段落
                    for j in range(i+2, min(i+10, len(doc.paragraphs))):
                        next_para = doc.paragraphs[j]
                        if next_para.text.strip() and next_para.runs:
                            font_size = next_para.runs[0].font.size
                            if font_size:
                                print(f'正文字体大小: {font_size.pt}pt (期望: 13pt)')
                                break
                    break
            
            print('\n✅ API字体大小配置测试完成')
            
        else:
            print(f'❌ API测试失败: {response.status_code}')
            print(f'错误信息: {response.text}')
            
    except Exception as e:
        print(f'❌ 测试过程中出错: {str(e)}')
    finally:
        files['file'].close()

if __name__ == "__main__":
    test_font_api()
