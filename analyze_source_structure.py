#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
分析源文档结构，查找绪论和Chinese Library Classification的位置
"""

from docx import Document
import re

def analyze_document_structure():
    """分析文档结构"""
    print("🔍 分析源文档结构...")
    
    try:
        doc = Document("jieguo.docx")
        
        print(f"📄 文档总段落数: {len(doc.paragraphs)}")
        print("\n" + "="*80)
        
        # 查找关键内容
        chinese_abstract_found = False
        english_abstract_found = False
        chinese_classification_found = False
        xulunn_found = False
        
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if not text:
                continue
                
            # 查找中文摘要
            if ('摘要' in text or '摘 要' in text) and not chinese_abstract_found:
                print(f"📍 第{i+1}段 - 中文摘要开始: {text}")
                chinese_abstract_found = True
                
            # 查找关键词
            elif '关键词' in text and chinese_abstract_found:
                print(f"📍 第{i+1}段 - 中文关键词: {text}")
                
            # 查找中图分类号
            elif '中图分类号' in text:
                print(f"📍 第{i+1}段 - 中图分类号: {text}")
                
            # 查找英文摘要
            elif 'Abstract' in text and not english_abstract_found:
                print(f"📍 第{i+1}段 - 英文摘要开始: {text}")
                english_abstract_found = True
                
            # 查找英文关键词
            elif 'Keywords' in text and english_abstract_found:
                print(f"📍 第{i+1}段 - 英文关键词: {text}")
                
            # 查找Chinese Library Classification
            elif 'Chinese Library Classification' in text:
                print(f"📍 第{i+1}段 - 英文分类号: {text}")
                chinese_classification_found = True
                
            # 查找绪论
            elif '绪论' in text and not xulunn_found:
                print(f"📍 第{i+1}段 - 绪论: {text}")
                xulunn_found = True
                
            # 查找章标题模式
            elif re.match(r'^第[一二三四五六七八九十\d]+章', text):
                print(f"📍 第{i+1}段 - 章标题: {text}")
                
            # 查找节标题模式
            elif re.match(r'^\d+\.\d+', text):
                print(f"📍 第{i+1}段 - 节标题: {text}")
        
        print("\n" + "="*80)
        print("🔍 关键发现:")
        
        if not chinese_classification_found:
            print("❌ 未找到'Chinese Library Classification'")
        else:
            print("✅ 找到'Chinese Library Classification'")
            
        if not xulunn_found:
            print("❌ 未找到'绪论'")
        else:
            print("✅ 找到'绪论'")
            
        # 显示所有段落的内容概览
        print(f"\n📋 所有{len(doc.paragraphs)}段内容概览:")
        print("-" * 80)
        for i in range(len(doc.paragraphs)):
            text = doc.paragraphs[i].text.strip()
            if text:
                print(f"{i+1:3d}: {text[:150]}{'...' if len(text) > 150 else ''}")
                
    except Exception as e:
        print(f"❌ 分析文档时出错: {e}")

if __name__ == "__main__":
    analyze_document_structure()
