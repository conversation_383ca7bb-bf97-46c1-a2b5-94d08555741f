#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
复杂文档处理测试脚本
测试表格、图片、公式等复杂元素的提取和重排版功能
"""

from word_generator import WordDocumentGenerator
from docx import Document
import os

def analyze_original_document():
    """分析原始文档的结构"""
    print("=== 分析原始testdoc.docx文档结构 ===")
    
    try:
        doc = Document('testdoc.docx')
        
        print(f"段落总数: {len(doc.paragraphs)}")
        print(f"表格总数: {len(doc.tables)}")
        
        # 分析表格结构
        for i, table in enumerate(doc.tables):
            print(f"\n表格 {i+1}:")
            print(f"  行数: {len(table.rows)}")
            print(f"  列数: {len(table.columns) if table.rows else 0}")
            
            # 显示表格的前几行内容
            if len(table.rows) > 0:
                print("  表头内容:")
                for j, cell in enumerate(table.rows[0].cells[:5]):  # 只显示前5列
                    cell_text = cell.text.strip()[:30]  # 只显示前30个字符
                    print(f"    列{j+1}: {cell_text}...")
                
                if len(table.rows) > 1:
                    print("  第一行数据:")
                    for j, cell in enumerate(table.rows[1].cells[:5]):
                        cell_text = cell.text.strip()[:30]
                        print(f"    列{j+1}: {cell_text}...")
        
        # 分析段落样式
        style_count = {}
        for para in doc.paragraphs:
            style_name = para.style.name
            style_count[style_name] = style_count.get(style_name, 0) + 1
        
        print(f"\n段落样式统计:")
        for style, count in sorted(style_count.items()):
            print(f"  {style}: {count}")
            
    except Exception as e:
        print(f"分析文档时出错: {str(e)}")

def test_content_extraction():
    """测试内容提取功能"""
    print("\n=== 测试内容提取功能 ===")
    
    generator = WordDocumentGenerator()
    
    # 提取内容
    print("正在提取testdoc.docx的内容...")
    extracted_content = generator.extract_content_from_docx('testdoc.docx')
    
    if extracted_content:
        print(f"提取成功！内容长度: {len(extracted_content)} 字符")
        
        # 分析提取的内容
        lines = extracted_content.split('\n')
        print(f"总行数: {len(lines)}")
        
        # 统计各种元素
        table_start_count = extracted_content.count('<!-- 表格开始 -->')
        table_end_count = extracted_content.count('<!-- 表格结束 -->')
        markdown_table_rows = extracted_content.count('|')
        
        print(f"表格开始标记: {table_start_count}")
        print(f"表格结束标记: {table_end_count}")
        print(f"Markdown表格行: {markdown_table_rows}")
        
        # 保存提取的内容到文件
        with open('extracted_content.md', 'w', encoding='utf-8') as f:
            f.write(extracted_content)
        print("提取的内容已保存到 extracted_content.md")
        
        return extracted_content
    else:
        print("内容提取失败")
        return None

def test_document_regeneration(extracted_content):
    """测试文档重新生成功能"""
    print("\n=== 测试文档重新生成功能 ===")
    
    if not extracted_content:
        print("没有提取的内容，跳过重新生成测试")
        return
    
    generator = WordDocumentGenerator()
    
    try:
        # 生成重排版文档
        doc_path = generator.generate_document(
            content=extracted_content,
            title="testdoc完整重排版文档",
            author="文档处理系统",
            company="自动化排版系统",
            header_text="重排版文档 - 包含表格",
            footer_text="第 {page} 页 | 自动生成",
            first_page_header="完整重排版测试",
            first_page_footer="包含原始表格数据",
            first_page_title="testdoc完整重排版文档",
            first_page_subtitle="保留表格和复杂元素的自动排版",
            first_page_author="文档处理系统",
            title_font_size=18,
            heading_font_size=16,
            body_font_size=12,
            line_spacing=1.5,
            paragraph_spacing=12
        )
        
        print(f"重排版文档生成成功: {doc_path}")
        
        # 分析生成的文档
        analyze_generated_document(doc_path)
        
    except Exception as e:
        print(f"文档生成失败: {str(e)}")

def analyze_generated_document(doc_path):
    """分析生成的文档"""
    print(f"\n=== 分析生成的文档: {os.path.basename(doc_path)} ===")
    
    try:
        doc = Document(doc_path)
        
        print(f"生成文档段落总数: {len(doc.paragraphs)}")
        print(f"生成文档表格总数: {len(doc.tables)}")
        
        # 分析生成的表格
        for i, table in enumerate(doc.tables):
            print(f"\n生成的表格 {i+1}:")
            print(f"  行数: {len(table.rows)}")
            print(f"  列数: {len(table.columns) if table.rows else 0}")
            
            # 显示表格样式
            print(f"  表格样式: {table.style.name if table.style else '无'}")
            
            # 显示部分内容
            if len(table.rows) > 0:
                print("  表头内容:")
                for j, cell in enumerate(table.rows[0].cells[:3]):  # 只显示前3列
                    cell_text = cell.text.strip()[:20]
                    print(f"    列{j+1}: {cell_text}...")
        
        print(f"\n文档文件大小: {os.path.getsize(doc_path)} 字节")
        
    except Exception as e:
        print(f"分析生成文档时出错: {str(e)}")

def test_table_formatting():
    """测试表格格式化功能"""
    print("\n=== 测试表格格式化功能 ===")
    
    # 创建一个包含表格的测试内容
    test_content = """
# 测试文档

## 数据统计表

<!-- 表格开始 -->
| 项目 | 数值 | 百分比 | 备注 |
| --- | --- | --- | --- |
| 项目A | 100 | 25% | 重要项目 |
| 项目B | 150 | 37.5% | 核心业务 |
| 项目C | 80 | 20% | 辅助功能 |
| 项目D | 70 | 17.5% | 其他 |
<!-- 表格结束 -->

## 总结

以上是测试表格的数据。

## 另一个表格

<!-- 表格开始 -->
| 姓名 | 年龄 | 部门 |
| --- | --- | --- |
| 张三 | 28 | 技术部 |
| 李四 | 32 | 市场部 |
| 王五 | 25 | 人事部 |
<!-- 表格结束 -->

测试完成。
"""
    
    generator = WordDocumentGenerator()
    
    try:
        doc_path = generator.generate_document(
            content=test_content,
            title="表格格式化测试文档",
            author="测试系统",
            company="测试公司",
            first_page_title="表格格式化测试",
            first_page_subtitle="验证表格处理功能"
        )
        
        print(f"表格测试文档生成成功: {doc_path}")
        analyze_generated_document(doc_path)
        
    except Exception as e:
        print(f"表格测试失败: {str(e)}")

if __name__ == "__main__":
    print("开始复杂文档处理测试...")
    
    # 1. 分析原始文档
    analyze_original_document()
    
    # 2. 测试内容提取
    extracted_content = test_content_extraction()
    
    # 3. 测试文档重新生成
    test_document_regeneration(extracted_content)
    
    # 4. 测试表格格式化
    test_table_formatting()
    
    print("\n=== 测试完成 ===")
    print("请检查outputs目录中生成的文档文件")
    print("请检查extracted_content.md文件查看提取的内容")
