#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
毕业论文格式化器 - 基于jieguo.docx样板
专门用于生成符合毕业论文格式要求的文档
"""

from docx import Document
from docx.shared import Inches, Pt, RGBColor
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.section import WD_ORIENT, WD_SECTION
from docx.oxml.shared import OxmlElement, qn
import os
import shutil
from datetime import datetime

class ThesisFormatter:
    """毕业论文格式化器 - 基于jieguo.docx样板"""
    
    def __init__(self):
        self.document = None
        
    def format_thesis_document(self, source_path,
                              thesis_title="XXXXXXXXXXXXXX系统的设计与应用",
                              student_name="",
                              student_id="",
                              department="智能科学与技术系",
                              major="",
                              class_name="",
                              supervisor="赵兵兵",
                              supervisor_title="副高",
                              enterprise_supervisor="",
                              thesis_date="二○二五年四月",
                              school_name="江西泰豪动漫职业学院",
                              year="2025",
                              output_dir="outputs"):
        """
        格式化毕业论文文档
        
        Args:
            source_path: 源文档路径
            thesis_title: 论文题目
            student_name: 学生姓名
            student_id: 学号
            department: 院系
            major: 专业
            class_name: 班级
            supervisor: 指导教师
            supervisor_title: 职称
            enterprise_supervisor: 企业导师
            thesis_date: 论文日期
            school_name: 学校名称
            year: 年份
            output_dir: 输出目录
            
        Returns:
            str: 生成的文档路径
        """
        
        if not os.path.exists(source_path):
            raise FileNotFoundError(f"源文档不存在: {source_path}")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成输出文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_filename = f"毕业论文_{student_name or '学生'}_{timestamp}.docx"
        output_path = os.path.join(output_dir, output_filename)
        
        # 复制原文档到输出位置
        shutil.copy2(source_path, output_path)
        
        # 打开复制的文档进行修改
        self.document = Document(output_path)
        
        try:
            # 1. 清空现有内容，重新构建
            self._clear_document()
            
            # 2. 设置页面属性
            self._setup_page_settings()
            
            # 3. 创建封面页
            self._create_cover_page(thesis_title, student_name, student_id, department, 
                                  major, class_name, supervisor, supervisor_title, 
                                  enterprise_supervisor, thesis_date)
            
            # 4. 添加分节符，开始新节
            self._add_section_break()
            
            # 5. 创建诚信声明页
            self._create_declaration_page(thesis_title, student_name, supervisor)
            
            # 6. 添加分节符，开始摘要节
            self._add_section_break()
            
            # 7. 创建摘要页
            self._create_abstract_page()
            
            # 8. 添加分节符，开始正文节
            self._add_section_break()
            
            # 9. 插入原文档内容
            self._insert_original_content(source_path)
            
            # 10. 设置页眉页脚
            self._setup_headers_footers(school_name, year)
            
            # 11. 保存修改后的文档
            self.document.save(output_path)
            
            return output_path
            
        except Exception as e:
            # 如果处理失败，删除输出文件
            if os.path.exists(output_path):
                os.remove(output_path)
            raise e
    
    def _clear_document(self):
        """清空文档内容"""
        # 删除所有段落（保留第一个）
        for i in range(len(self.document.paragraphs) - 1, 0, -1):
            p = self.document.paragraphs[i]._element
            p.getparent().remove(p)
        
        # 清空第一个段落
        if self.document.paragraphs:
            self.document.paragraphs[0].clear()
    
    def _setup_page_settings(self):
        """设置页面属性"""
        for section in self.document.sections:
            # 设置页边距（基于样板）
            section.top_margin = Inches(0.79)
            section.bottom_margin = Inches(0.79)
            section.left_margin = Inches(1.25)
            section.right_margin = Inches(1.25)
            
            # 设置页面大小（A4）
            section.page_width = Inches(8.27)
            section.page_height = Inches(11.69)
    
    def _create_cover_page(self, thesis_title, student_name, student_id, department,
                          major, class_name, supervisor, supervisor_title, 
                          enterprise_supervisor, thesis_date):
        """创建封面页"""
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 毕业论文标题
        title_para = self.document.add_paragraph()
        title_run = title_para.add_run("毕业论文")
        title_run.font.name = '黑体'
        title_run.font.size = Pt(42)
        title_run.font.bold = True
        title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 论文题目
        thesis_title_para = self.document.add_paragraph()
        thesis_title_run = thesis_title_para.add_run(thesis_title)
        thesis_title_run.font.name = '黑体'
        thesis_title_run.font.size = Pt(22)
        thesis_title_run.font.bold = True
        thesis_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        self.document.add_paragraph()
        
        # 学生信息表格式
        info_items = [
            ("院    系：", department),
            ("姓    名：", student_name),
            ("学    号：", student_id),
            ("专    业：", major),
            ("班    级：", class_name),
            ("指导教师：", supervisor),
            ("职    称：", supervisor_title),
            ("企业导师：", enterprise_supervisor)
        ]
        
        for label, value in info_items:
            info_para = self.document.add_paragraph()
            
            # 标签部分
            label_run = info_para.add_run(label)
            label_run.font.name = '宋体'
            label_run.font.size = Pt(14)
            
            # 内容部分
            if value:
                content_run = info_para.add_run(f"   {value}")
                content_run.font.name = '宋体'
                content_run.font.size = Pt(14)
            
            info_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        for _ in range(3):
            self.document.add_paragraph()
        
        # 日期
        date_para = self.document.add_paragraph()
        date_run = date_para.add_run(thesis_date)
        date_run.font.name = '楷体_GB2312'
        date_run.font.size = Pt(18)
        date_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    def _add_section_break(self):
        """添加分节符"""
        para = self.document.add_paragraph()
        run = para.add_run()
        run.add_break(break_type=6)  # 分页符
    
    def _create_declaration_page(self, thesis_title, student_name, supervisor):
        """创建诚信声明页"""
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 诚信声明标题
        decl_title_para = self.document.add_paragraph()
        decl_title_run = decl_title_para.add_run("毕业论文诚信声明")
        decl_title_run.font.name = '黑体'
        decl_title_run.font.size = Pt(16)
        decl_title_run.font.bold = True
        decl_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 声明内容
        decl_content_para = self.document.add_paragraph()
        decl_content_run = decl_content_para.add_run("本人郑重声明：")
        decl_content_run.font.name = '宋体'
        decl_content_run.font.size = Pt(14)
        
        decl_text_para = self.document.add_paragraph()
        decl_text = f"所呈交的毕业论文《{thesis_title}》是本人在指导老师的指导下，独立研究、写作的成果。毕业设计(论文)中所引用是他人的无论以何种方式发布的文字、研究成果，均在毕业设计(论文)中以明确方式标明。"
        decl_text_run = decl_text_para.add_run(decl_text)
        decl_text_run.font.name = '宋体'
        decl_text_run.font.size = Pt(14)
        decl_text_para.paragraph_format.first_line_indent = Inches(0.5)
        
        decl_result_para = self.document.add_paragraph()
        decl_result_run = decl_result_para.add_run("本声明的法律结果由本人独自承担。")
        decl_result_run.font.name = '宋体'
        decl_result_run.font.size = Pt(14)
        decl_result_para.paragraph_format.first_line_indent = Inches(0.5)
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 签名栏
        sign_para1 = self.document.add_paragraph()
        sign_run1 = sign_para1.add_run("作  者 签 名：")
        sign_run1.font.name = '宋体'
        sign_run1.font.size = Pt(14)
        
        sign_para2 = self.document.add_paragraph()
        sign_run2 = sign_para2.add_run("指导教师签名：")
        sign_run2.font.name = '宋体'
        sign_run2.font.size = Pt(14)
        
        date_sign_para = self.document.add_paragraph()
        date_sign_run = date_sign_para.add_run("年    月    日")
        date_sign_run.font.name = '宋体'
        date_sign_run.font.size = Pt(14)
    
    def _create_abstract_page(self):
        """创建摘要页"""
        
        # 添加空行
        for _ in range(2):
            self.document.add_paragraph()
        
        # 摘要标题
        abstract_title_para = self.document.add_paragraph()
        abstract_title_run = abstract_title_para.add_run("摘    要")
        abstract_title_run.font.name = '黑体'
        abstract_title_run.font.size = Pt(16)
        abstract_title_run.font.bold = True
        abstract_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加空行
        self.document.add_paragraph()
        
        # 摘要内容（示例）
        abstract_paras = [
            "20世纪以来，建构主义成为风靡全球的理论流派，××××××××××××××××××，××××××××××××××××××××××××××××××。×××××××××××，××××××××××××××××××××××××××。",
            "本研究以建构主义作为基本的理论基础，××××××××××××××××××××××××××××××。×××××××××××，××××××××××××××××××××××××××。",
            "…………",
            "××××××，××××××××××××××××××××××××××××××。×××××××××××，××××××××××××××××××××××××××。"
        ]
        
        for abstract_text in abstract_paras:
            abstract_para = self.document.add_paragraph()
            abstract_run = abstract_para.add_run(abstract_text)
            abstract_run.font.name = '宋体'
            abstract_run.font.size = Pt(12)
            abstract_para.paragraph_format.first_line_indent = Inches(0.5)
            abstract_para.paragraph_format.line_spacing = 1.5
        
        # 添加空行
        self.document.add_paragraph()
        
        # 关键词
        keywords_para = self.document.add_paragraph()
        keywords_run = keywords_para.add_run("关键词：课程知识；建构；建构主义；社会建构；个体建构")
        keywords_run.font.name = '宋体'
        keywords_run.font.size = Pt(12)
    
    def _insert_original_content(self, source_path):
        """插入原文档内容"""
        try:
            source_doc = Document(source_path)

            # 添加正文标题
            content_title_para = self.document.add_paragraph()
            content_title_run = content_title_para.add_run("正文内容")
            content_title_run.font.name = '黑体'
            content_title_run.font.size = Pt(16)
            content_title_run.font.bold = True
            content_title_para.alignment = WD_ALIGN_PARAGRAPH.CENTER

            self.document.add_paragraph()  # 空行

            # 简化处理：复制所有段落内容
            for para in source_doc.paragraphs:
                if para.text.strip():  # 只复制非空段落
                    new_para = self.document.add_paragraph()
                    new_para.text = para.text

                    # 设置基本格式
                    for run in new_para.runs:
                        run.font.name = '宋体'
                        run.font.size = Pt(12)

                    # 设置段落格式
                    new_para.paragraph_format.first_line_indent = Inches(0.5)
                    new_para.paragraph_format.line_spacing = 1.5

            # 复制表格
            for table in source_doc.tables:
                new_table = self.document.add_table(len(table.rows), len(table.columns))
                new_table.style = 'Table Grid'

                for i, row in enumerate(table.rows):
                    for j, cell in enumerate(row.cells):
                        new_table.rows[i].cells[j].text = cell.text
                        # 设置表格字体
                        for para in new_table.rows[i].cells[j].paragraphs:
                            for run in para.runs:
                                run.font.name = '宋体'
                                run.font.size = Pt(10)

        except Exception as e:
            print(f"插入原文档内容时出错: {e}")
            # 如果插入失败，添加一个占位符
            placeholder_para = self.document.add_paragraph()
            placeholder_run = placeholder_para.add_run("[原文档内容将在此处显示]")
            placeholder_run.font.name = '宋体'
            placeholder_run.font.size = Pt(12)
    
    def _setup_headers_footers(self, school_name, year):
        """设置页眉页脚"""
        sections = self.document.sections
        
        # 第一节（封面）- 只有页脚页码
        if len(sections) > 0:
            section1 = sections[0]
            section1.different_first_page_header_footer = False
            
            footer1 = section1.footer
            footer1_para = footer1.paragraphs[0]
            footer1_para.text = "3"
            footer1_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 第二节（声明页）- 页眉 + 首页单独设置
        if len(sections) > 1:
            section2 = sections[1]
            section2.different_first_page_header_footer = True
            
            # 设置页眉
            header2 = section2.header
            header2_para = header2.paragraphs[0]
            header2_para.text = f"{school_name}{year}届学生毕业设计（说明书）"
            header2_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            header2_run = header2_para.runs[0]
            header2_run.font.name = '宋体'
            header2_run.font.size = Pt(10)
        
        # 第三节（摘要）- 页眉 + 罗马数字页码
        if len(sections) > 2:
            section3 = sections[2]
            
            # 设置页眉
            header3 = section3.header
            header3_para = header3.paragraphs[0]
            header3_para.text = f"{school_name}{year}届学生毕业设计（说明书）"
            header3_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 设置页脚（罗马数字）
            footer3 = section3.footer
            footer3_para = footer3.paragraphs[0]
            footer3_para.text = "II"
            footer3_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 第四节（正文）- 页眉 + 阿拉伯数字页码
        if len(sections) > 3:
            section4 = sections[3]
            
            # 设置页眉
            header4 = section4.header
            header4_para = header4.paragraphs[0]
            header4_para.text = f"{school_name}{year}届学生毕业设计（说明书）"
            header4_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
            
            # 设置页脚（阿拉伯数字）
            footer4 = section4.footer
            footer4_para = footer4.paragraphs[0]
            footer4_para.text = "5"
            footer4_para.alignment = WD_ALIGN_PARAGRAPH.CENTER
