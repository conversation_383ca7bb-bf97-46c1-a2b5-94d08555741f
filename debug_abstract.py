#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试摘要识别问题
"""

from docx import Document

def debug_abstract():
    """调试摘要识别问题"""
    
    source_doc = Document('testdoc.docx')
    
    print('=== 调试摘要识别问题 ===')
    
    # 检查前20段的具体内容
    for i, para in enumerate(source_doc.paragraphs[:20]):
        text = para.text.strip()
        if text:
            print(f'第{i+1}段: "{text}"')
            # 检查是否包含摘要相关字符
            if '摘' in text:
                print(f'  -> 包含"摘"字')
            if '要' in text:
                print(f'  -> 包含"要"字')
            if '摘要' in text:
                print(f'  -> 包含"摘要"')
            if '关键词' in text:
                print(f'  -> 包含"关键词"')

if __name__ == "__main__":
    debug_abstract()
