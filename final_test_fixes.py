#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终测试修复后的功能
"""

import requests
import os
from docx import Document

def final_test_fixes():
    """最终测试修复后的功能"""
    
    print('=== 最终测试修复后的功能 ===')
    
    url = 'http://localhost:5000/format-thesis'
    
    # 准备文件和数据
    files = {'file': open('testdoc.docx', 'rb')}
    data = {
        'thesis_title': '基于人工智能的文档处理系统设计与实现',
        'student_name': '最终修复测试',
        'student_id': '2021001252',
        'department': '智能科学与技术系',
        'major': '人工智能技术应用',
        'class_name': 'AI2101班',
        'supervisor': '最终教授',
        'supervisor_title': '教授',
        'enterprise_supervisor': '最终工程师',
        'school_name': '江西泰豪动漫职业学院',
        'year': '2025',
        'thesis_date': '二○二五年七月',
        
        # 测试字体大小配置
        'section_title_size': '18',
        'body_text_size': '13',
    }
    
    try:
        print('正在测试最终修复版本API...')
        response = requests.post(url, files=files, data=data)
        
        if response.status_code == 200:
            # 保存返回的文件
            with open('final_fixes_test.docx', 'wb') as f:
                f.write(response.content)
            print('✅ 最终修复版本API测试成功！')
            print(f'文件已保存: final_fixes_test.docx')
            print(f'文件大小: {len(response.content)} 字节')
            
            # 分析生成的文档
            doc = Document('final_fixes_test.docx')
            print(f'段落数: {len(doc.paragraphs)}')
            print(f'表格数: {len(doc.tables)}')
            
            # 检查诚信声明签名
            print('\n=== 诚信声明签名检查 ===')
            signature_check = {
                'author_signature': False,
                'supervisor_signature': False,
                'date_line': False,
                'right_aligned': False,
                'has_underline': False
            }
            
            for i, para in enumerate(doc.paragraphs):
                text = para.text.strip()
                if '作  者 签 名' in text:
                    signature_check['author_signature'] = True
                    signature_check['right_aligned'] = (para.alignment == 2)  # RIGHT = 2
                    signature_check['has_underline'] = ('___' in text)
                    print(f'  作者签名: {text}')
                    print(f'  右对齐: {signature_check["right_aligned"]}')
                    print(f'  有下划线: {signature_check["has_underline"]}')
                elif '指导教师签名' in text:
                    signature_check['supervisor_signature'] = True
                    print(f'  指导教师签名: {text}')
                elif '年    月    日' in text:
                    signature_check['date_line'] = True
                    print(f'  日期行: {text}')
            
            # 检查目录结构
            print('\n=== 目录结构检查 ===')
            toc_check = {
                'toc_found': False,
                'chapter_count': 0,
                'section_count': 0,
                'subsection_count': 0,
                'has_real_content': False
            }
            
            # 查找目录表格
            for table in doc.tables:
                if len(table.rows) > 10:  # 目录表格
                    first_row = table.rows[0].cells[0].text.strip()
                    if '摘要' in first_row or '目录' in first_row:
                        toc_check['toc_found'] = True
                        print(f'找到目录表格: {len(table.rows)}行')
                        
                        # 分析目录内容
                        for row in table.rows:
                            title = row.cells[0].text.strip()
                            if title:
                                # 检查是否包含实际文档内容
                                if '研究' in title or '背景' in title or '创新点' in title:
                                    toc_check['has_real_content'] = True
                                
                                # 统计层次
                                if ('第' in title and '章' in title) or (title.startswith('1 ') or title.startswith('2 ')):
                                    toc_check['chapter_count'] += 1
                                elif title.count('.') == 1 and title[0].isdigit():
                                    toc_check['section_count'] += 1
                                elif title.count('.') == 2 and title[0].isdigit():
                                    toc_check['subsection_count'] += 1
                        
                        print(f'  章数量: {toc_check["chapter_count"]}')
                        print(f'  节数量: {toc_check["section_count"]}')
                        print(f'  子节数量: {toc_check["subsection_count"]}')
                        print(f'  包含实际内容: {toc_check["has_real_content"]}')
                        break
            
            # 总结检查结果
            print('\n=== 修复效果总结 ===')
            
            # 诚信声明检查
            if all([signature_check['author_signature'], signature_check['supervisor_signature'], 
                   signature_check['right_aligned'], signature_check['has_underline']]):
                print('✅ 诚信声明签名部分完全正确')
            else:
                print('❌ 诚信声明签名部分有问题')
                for key, value in signature_check.items():
                    print(f'  {key}: {value}')
            
            # 目录检查
            if (toc_check['toc_found'] and toc_check['has_real_content'] and 
                toc_check['section_count'] > 0):
                print('✅ 目录结构基于实际文档内容生成')
            else:
                print('❌ 目录结构可能有问题')
                for key, value in toc_check.items():
                    print(f'  {key}: {value}')
            
            # 整体评估
            if (all(signature_check.values()) and toc_check['toc_found'] and 
                toc_check['has_real_content']):
                print('\n🎉 所有修复都成功！文档质量完全符合要求！')
            else:
                print('\n⚠️ 部分功能需要进一步优化')
            
        else:
            print(f'❌ API测试失败: {response.status_code}')
            print(f'错误信息: {response.text}')
            
    except Exception as e:
        print(f'❌ 测试过程中出错: {str(e)}')
    finally:
        files['file'].close()

if __name__ == "__main__":
    final_test_fixes()
