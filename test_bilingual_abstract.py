#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试中英文摘要处理
"""

from thesis_formatter_v3 import ThesisFormatterV3
from docx import Document

def test_bilingual_abstract():
    """测试中英文摘要处理"""
    
    print('=== 测试中英文摘要处理 ===')
    
    formatter = ThesisFormatterV3()
    
    try:
        output_path = formatter.format_thesis_document(
            source_path='testdoc.docx',
            thesis_title='基于人工智能的文档处理系统设计与实现',
            student_name='中英文测试',
            student_id='2021001246',
            department='智能科学与技术系',
            major='人工智能技术应用',
            class_name='AI2101班',
            supervisor='双语教授',
            supervisor_title='教授',
            enterprise_supervisor='双语工程师',
            thesis_date='二○二五年七月',
            school_name='江西泰豪动漫职业学院',
            year='2025'
        )
        
        print(f'✅ 中英文摘要版本生成成功: {output_path}')
        
        # 分析生成的文档
        doc = Document(output_path)
        
        print(f'段落总数: {len(doc.paragraphs)}')
        print(f'表格总数: {len(doc.tables)}')
        
        # 检查中英文摘要
        print('\n=== 中英文摘要检查 ===')
        
        chinese_abstract_found = False
        english_abstract_found = False
        chinese_bjse_count = 0
        english_bse_count = 0
        
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            
            # 检查中文摘要
            if ('摘 要' in text or '摘要' in text) and not chinese_abstract_found:
                print(f'中文摘要标题: 第{i+1}段 - "{text}"')
                chinese_abstract_found = True
                # 检查中文摘要内容
                for j in range(i+1, min(i+10, len(doc.paragraphs))):
                    next_text = doc.paragraphs[j].text.strip()
                    if next_text:
                        if '北京证券交易所' in next_text:
                            print(f'  ✅ 中文摘要内容: 第{j+1}段 - 北京证券交易所相关内容')
                            chinese_bjse_count += 1
                        elif '关键词' in next_text:
                            print(f'  ✅ 中文关键词: 第{j+1}段 - {next_text[:50]}...')
                            break
            
            # 检查英文摘要
            elif 'Abstract' in text and not english_abstract_found:
                print(f'英文摘要标题: 第{i+1}段 - "{text}"')
                english_abstract_found = True
                # 检查英文摘要内容
                for j in range(i+1, min(i+10, len(doc.paragraphs))):
                    next_text = doc.paragraphs[j].text.strip()
                    if next_text:
                        if 'Beijing Stock Exchange' in next_text or 'BSE' in next_text:
                            print(f'  ✅ 英文摘要内容: 第{j+1}段 - Beijing Stock Exchange相关内容')
                            english_bse_count += 1
                        elif 'Keywords' in next_text:
                            print(f'  ✅ 英文关键词: 第{j+1}段 - {next_text[:50]}...')
                            break
        
        # 检查正文内容
        print('\n=== 正文内容检查 ===')
        content_found = False
        content_bjse_count = 0
        content_bse_count = 0
        
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            if '正文内容' in text:
                print(f'正文标题: 第{i+1}段')
                content_found = True
                # 统计正文中的相关内容
                for j in range(i+1, len(doc.paragraphs)):
                    next_text = doc.paragraphs[j].text.strip()
                    if '北京证券交易所' in next_text:
                        content_bjse_count += 1
                    if 'Beijing Stock Exchange' in next_text or 'BSE' in next_text:
                        content_bse_count += 1
                break
        
        # 总结检查结果
        print(f'\n=== 内容分布总结 ===')
        print(f'中文摘要中"北京证券交易所"出现次数: {chinese_bjse_count}')
        print(f'英文摘要中"Beijing Stock Exchange/BSE"出现次数: {english_bse_count}')
        print(f'正文中"北京证券交易所"出现次数: {content_bjse_count}')
        print(f'正文中"Beijing Stock Exchange/BSE"出现次数: {content_bse_count}')
        
        # 验证结果
        if chinese_abstract_found and english_abstract_found:
            print('✅ 中英文摘要都成功生成')
        else:
            print('❌ 中英文摘要生成不完整')
        
        if chinese_bjse_count == 1 and english_bse_count >= 1:
            print('✅ 中英文摘要内容正确提取')
        else:
            print('❌ 中英文摘要内容提取可能有问题')
        
        return output_path
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    test_bilingual_abstract()
