#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试所有修复功能
"""

from thesis_formatter_v3 import ThesisFormatterV3
import os

def test_all_fixes():
    """测试所有修复功能"""
    print("🔧 开始测试所有修复功能...")
    
    # 源文件路径
    source_file = "jieguo.docx"
    
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return
    
    try:
        # 创建格式化器
        formatter = ThesisFormatterV3()
        
        # 设置学生信息
        student_info = {
            'name': '李哲龙',
            'student_id': '2021001',
            'major': '计算机科学与技术',
            'class_name': '计算机2101班',
            'advisor': '张教授',
            'college': '计算机学院'
        }
        
        # 格式化文档，包含自定义字体大小
        output_path = formatter.format_thesis_document(
            source_file, 
            student_info=student_info,
            output_name="全面修复测试",
            # 自定义字体大小
            chapter_title_size=18,  # 章标题字体大小
            section_title_size=16,  # 节标题字体大小
            body_text_size=12       # 正文字体大小
        )
        
        if output_path:
            print(f"✅ 全面修复测试完成！")
            print(f"📄 输出文件: {output_path}")
            print("\n🔍 修复内容:")
            print("  ✓ 中图分类号包含在摘要范围内")
            print("  ✓ 移除'正文内容'标识")
            print("  ✓ 章标题居中加粗（黑体18pt）")
            print("  ✓ 节标题左对齐加粗（黑体16pt）")
            print("  ✓ 正文首行缩进（宋体12pt）")
            print("  ✓ 目录格式规范（点线连接）")
            print("  ✓ 字体大小可配置")
        else:
            print("❌ 全面修复测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_all_fixes()
