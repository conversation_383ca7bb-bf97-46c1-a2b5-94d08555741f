#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试最终修复后的毕业论文格式化器
"""

from thesis_formatter import ThesisFormatter
from docx import Document

def test_final_thesis_formatter():
    """测试最终修复后的毕业论文格式化器"""
    
    print('=== 测试最终修复后的毕业论文格式化器 ===')
    
    formatter = ThesisFormatter()
    
    try:
        output_path = formatter.format_thesis_document(
            source_path='testdoc.docx',
            thesis_title='基于人工智能的文档处理系统设计与实现',
            student_name='李四',
            student_id='2021001235',
            department='智能科学与技术系',
            major='人工智能技术应用',
            class_name='AI2101班',
            supervisor='王教授',
            supervisor_title='教授',
            enterprise_supervisor='张工程师',
            thesis_date='二○二五年七月',
            school_name='江西泰豪动漫职业学院',
            year='2025'
        )
        
        print(f'✅ 最终版本生成成功: {output_path}')
        
        # 分析生成的文档
        doc = Document(output_path)
        
        print(f'段落总数: {len(doc.paragraphs)}')
        print(f'表格总数: {len(doc.tables)}')
        print(f'节数: {len(doc.sections)}')
        
        # 检查节结构
        print('\n节结构分析:')
        for i, section in enumerate(doc.sections):
            print(f'节{i+1}:')
            print(f'  页边距: 上{section.top_margin.inches:.2f}" 下{section.bottom_margin.inches:.2f}" 左{section.left_margin.inches:.2f}" 右{section.right_margin.inches:.2f}"')
            print(f'  不同首页: {section.different_first_page_header_footer}')
            
            # 检查页眉
            if section.header.paragraphs:
                header_text = section.header.paragraphs[0].text.strip()
                if header_text:
                    print(f'  页眉: {header_text}')
            
            # 检查页脚
            if section.footer.paragraphs:
                footer_text = section.footer.paragraphs[0].text.strip()
                if footer_text:
                    print(f'  页脚: {footer_text}')
        
        # 检查目录
        print('\n目录检查:')
        toc_count = 0
        toc_start = -1
        for i, para in enumerate(doc.paragraphs):
            if 'toc' in para.style.name.lower():
                if toc_start == -1:
                    toc_start = i + 1
                toc_count += 1
        
        print(f'目录条目数量: {toc_count}')
        if toc_start > 0:
            print(f'目录开始位置: 第{toc_start}段')
        
        # 检查首页信息表格
        print('\n首页表格检查:')
        cover_table_found = False
        for i, table in enumerate(doc.tables):
            if len(table.rows) == 8 and len(table.columns) == 2:
                print(f'  找到首页信息表格（表格{i+1}）: 8行2列')
                # 显示表格内容
                for j, row in enumerate(table.rows[:3]):  # 只显示前3行
                    label = row.cells[0].text.strip()
                    content = row.cells[1].text.strip()
                    print(f'    行{j+1}: {label} | {content}')
                cover_table_found = True
                break
        
        if not cover_table_found:
            print('  ❌ 未找到首页信息表格')
        
        # 检查原始内容保留情况
        print('\n原始内容保留检查:')
        original_tables = len(doc.tables) - 1  # 减去首页信息表格
        print(f'原始文档表格保留数量: {original_tables}')
        
        return output_path
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        import traceback
        traceback.print_exc()
        return None

def compare_with_jieguo_template(generated_path):
    """与jieguo.docx样板对比"""
    
    if not generated_path:
        print('❌ 没有生成的文档可供对比')
        return
    
    print('\n=== 与jieguo.docx样板对比 ===')
    
    try:
        template_doc = Document('jieguo.docx')
        generated_doc = Document(generated_path)
        
        print('基本结构对比:')
        print(f'  样板段落数: {len(template_doc.paragraphs)}')
        print(f'  生成段落数: {len(generated_doc.paragraphs)}')
        print(f'  样板节数: {len(template_doc.sections)}')
        print(f'  生成节数: {len(generated_doc.sections)}')
        print(f'  样板表格数: {len(template_doc.tables)}')
        print(f'  生成表格数: {len(generated_doc.tables)}')
        
        # 检查目录对比
        template_toc_count = 0
        generated_toc_count = 0
        
        for para in template_doc.paragraphs:
            if 'toc' in para.style.name.lower():
                template_toc_count += 1
        
        for para in generated_doc.paragraphs:
            if 'toc' in para.style.name.lower():
                generated_toc_count += 1
        
        print(f'\n目录对比:')
        print(f'  样板目录条目: {template_toc_count}')
        print(f'  生成目录条目: {generated_toc_count}')
        
        if template_toc_count > 0 and generated_toc_count > 0:
            print('  ✅ 两个文档都有目录')
        elif template_toc_count > 0:
            print('  ❌ 样板有目录，但生成文档没有')
        elif generated_toc_count > 0:
            print('  ✅ 生成文档有目录')
        
        # 检查节结构对比
        print(f'\n节结构对比:')
        if len(template_doc.sections) == len(generated_doc.sections):
            print('  ✅ 节数量一致')
        else:
            print(f'  ❌ 节数量不一致: 样板{len(template_doc.sections)}节 vs 生成{len(generated_doc.sections)}节')
        
    except Exception as e:
        print(f'❌ 对比时出错: {e}')

if __name__ == "__main__":
    print("开始测试最终修复后的毕业论文格式化器...")
    
    # 测试最终版本
    generated_doc = test_final_thesis_formatter()
    
    # 与样板对比
    compare_with_jieguo_template(generated_doc)
    
    print("\n=== 测试完成 ===")
    print("请检查outputs目录中的最新生成文档，验证格式是否符合要求。")
