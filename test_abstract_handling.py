#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试摘要处理功能
"""

from thesis_formatter_v3 import ThesisFormatterV3
from docx import Document
import os

def test_abstract_handling():
    """测试摘要处理功能"""
    
    print('=== 测试摘要处理功能 ===')
    
    formatter = ThesisFormatterV3()
    
    try:
        # 使用一个包含摘要的测试文档
        test_source = 'testdoc.docx'  # 或者使用其他包含摘要的文档
        
        if not os.path.exists(test_source):
            print(f'❌ 测试文档不存在: {test_source}')
            return None
        
        output_path = formatter.format_thesis_document(
            source_path=test_source,
            thesis_title='基于人工智能的文档处理系统设计与实现',
            student_name='李四',
            student_id='2021001235',
            department='智能科学与技术系',
            major='人工智能技术应用',
            class_name='AI2101班',
            supervisor='王教授',
            supervisor_title='教授',
            enterprise_supervisor='张工程师',
            thesis_date='二○二五年七月',
            school_name='江西泰豪动漫职业学院',
            year='2025'
        )
        
        print(f'✅ 文档生成成功: {output_path}')
        
        # 分析生成的文档
        doc = Document(output_path)
        
        print(f'段落总数: {len(doc.paragraphs)}')
        
        # 查找摘要部分
        print('\n摘要部分分析:')
        abstract_found = False
        abstract_start = -1
        keywords_found = False
        
        for i, para in enumerate(doc.paragraphs):
            text = para.text.strip()
            
            # 查找摘要标题
            if "摘    要" in text and not abstract_found:
                abstract_found = True
                abstract_start = i
                print(f'  摘要标题位置: 第{i+1}段')
            
            # 查找关键词
            if "关键词" in text and abstract_found:
                keywords_found = True
                print(f'  关键词位置: 第{i+1}段')
                print(f'  关键词内容: {text}')
                break
        
        if abstract_found and keywords_found:
            print(f'  ✅ 摘要部分完整: 从第{abstract_start+1}段到关键词')
            
            # 显示摘要内容
            print('\n摘要内容:')
            for i in range(abstract_start + 1, abstract_start + 10):  # 显示接下来的几段
                if i < len(doc.paragraphs):
                    text = doc.paragraphs[i].text.strip()
                    if text and "关键词" not in text:
                        print(f'  第{i+1}段: {text[:100]}{"..." if len(text) > 100 else ""}')
                    elif "关键词" in text:
                        break
        else:
            print('  ❌ 摘要部分不完整')
        
        # 检查是否有重复的摘要
        print('\n重复摘要检查:')
        abstract_count = 0
        for para in doc.paragraphs:
            if "摘    要" in para.text:
                abstract_count += 1
        
        if abstract_count == 1:
            print('  ✅ 没有重复的摘要标题')
        else:
            print(f'  ❌ 发现{abstract_count}个摘要标题，可能存在重复')
        
        return output_path
        
    except Exception as e:
        print(f'❌ 测试失败: {e}')
        return None

if __name__ == '__main__':
    test_abstract_handling() 