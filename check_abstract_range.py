#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查摘要区间识别
"""

from docx import Document

def check_abstract_range():
    """检查源文件中的摘要区间识别"""
    
    source_doc = Document('testdoc.docx')
    
    print('=== 检查摘要区间识别（摘要标题到关键词）===')
    
    abstract_start = -1
    abstract_end = -1
    
    for i, para in enumerate(source_doc.paragraphs):
        text = para.text.strip()
        if text:
            # 查找摘要标题
            if ('摘要' in text or '摘 要' in text) and abstract_start == -1:
                print(f'找到摘要开始: 第{i+1}段 - "{text}"')
                abstract_start = i
            # 查找关键词（摘要结束）
            elif abstract_start != -1 and '关键词' in text:
                print(f'找到摘要结束: 第{i+1}段 - "{text[:50]}..."')
                abstract_end = i
                break
    
    print(f'\n摘要区间: 第{abstract_start+1}段 到 第{abstract_end+1}段')
    
    if abstract_start != -1 and abstract_end != -1:
        print(f'\n摘要区间内容（共{abstract_end - abstract_start + 1}段）:')
        for i in range(abstract_start, abstract_end + 1):
            text = source_doc.paragraphs[i].text.strip()
            if text:
                print(f'第{i+1}段: {text[:80]}...')
    else:
        print('\n❌ 未找到完整的摘要区间')

if __name__ == "__main__":
    check_abstract_range()
