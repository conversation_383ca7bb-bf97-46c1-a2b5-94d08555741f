#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
最终目录修复测试
"""

from thesis_formatter_v3 import ThesisFormatterV3
import os

def final_toc_test():
    """最终目录修复测试"""
    print("🎯 开始最终目录修复测试...")
    
    # 源文件路径
    source_file = "jieguo.docx"
    
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return
    
    try:
        # 创建格式化器
        formatter = ThesisFormatterV3()
        
        # 设置学生信息
        student_info = {
            'name': '李哲龙',
            'student_id': '2021001',
            'major': '计算机科学与技术',
            'class_name': '计算机2101班',
            'advisor': '张教授',
            'college': '计算机学院'
        }
        
        # 格式化文档
        output_path = formatter.format_thesis_document(
            source_file, 
            student_info=student_info,
            output_name="最终目录修复"
        )
        
        if output_path:
            print(f"✅ 最终目录修复测试完成！")
            print(f"📄 输出文件: {output_path}")
            print("\n🔍 修复内容:")
            print("  ✓ 标题长度限制，避免换行")
            print("  ✓ 添加点线连接标题和页码")
            print("  ✓ 页码右对齐")
            print("  ✓ 层级缩进规范")
            print("  ✓ 字体格式统一")
        else:
            print("❌ 最终目录修复测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    final_toc_test()
