#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试目录修复功能
"""

from thesis_formatter_v3 import ThesisFormatterV3
import os

def test_toc_fix():
    """测试目录修复"""
    print("🔧 开始测试目录修复...")
    
    # 源文件路径
    source_file = "jieguo.docx"
    
    if not os.path.exists(source_file):
        print(f"❌ 源文件不存在: {source_file}")
        return
    
    try:
        # 创建格式化器
        formatter = ThesisFormatterV3()
        
        # 设置学生信息
        student_info = {
            'name': '李哲龙',
            'student_id': '2021001',
            'major': '计算机科学与技术',
            'class_name': '计算机2101班',
            'advisor': '张教授',
            'college': '计算机学院'
        }
        
        # 格式化文档
        output_path = formatter.format_thesis_document(
            source_file,
            student_info=student_info,
            output_name="目录修复测试"
        )
        
        if output_path:
            print(f"✅ 目录修复测试完成！")
            print(f"📄 输出文件: {output_path}")
        else:
            print("❌ 目录修复测试失败")
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_toc_fix()
